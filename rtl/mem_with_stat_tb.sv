`timescale 1ns/10ps

module memory_counter_optimized_tb;
    // 参数定义
    parameter ADDR_WIDTH = 8;  // 使用较小的地址宽度以便于测试
    parameter DATA_WIDTH = 64;
    parameter CLK_PERIOD = 10; // 10ns时钟周期
    
    // 信号定义
    reg clk;
    reg rst_n;
    reg ren;
    reg [ADDR_WIDTH-1:0] addr;
    wire [DATA_WIDTH-1:0] data_out;
    wire valid;
    
    // 实例化被测模块
    memory_counter_optimized #(
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH)
    ) dut (
        .clk(clk),
        .rst_n(rst_n),
        .ren(ren),
        .addr(addr),
        .data_out(data_out),
        .valid(valid)
    );
    
    // 时钟生成
    always #(CLK_PERIOD/2) clk = ~clk;
    
    // 测试数据
    reg [DATA_WIDTH-1:0] test_data [0:10];
    
    // 监控输出
    always @(posedge clk) begin
        if (valid) begin
            $display("Time %0t: Read address 0x%h, data 0x%h", $time, addr, data_out);
        end
    end
    
    // 初始化内存数据的任务
    task initialize_memory;
        integer i;
        begin
            for (i = 0; i < 20; i = i + 1) begin
                test_data[i] = {DATA_WIDTH{1'b0}} + i + 1;
                dut.mem[i] = test_data[i];
                dut.page_read_stat[i] = 0;
                dut.valid_table[i] = 1'b1;
            end
            $display("Memory initialization completed");
        end
    endtask
    
    // 检查计数器的任务
    task check_counter;
        input [ADDR_WIDTH-1:0] check_addr;
        input [63:0] expected_count;
        begin
            if (dut.page_read_stat[check_addr] == expected_count)
                $display("Counter value for address 0x%h is correct: %0d", check_addr, expected_count);
            else
                $display("Error: Counter value for address 0x%h is %0d, expected %0d", 
                         check_addr, dut.page_read_stat[check_addr], expected_count);
        end
    endtask
    
    // 读取操作的任务
    task read_addr;
        input [ADDR_WIDTH-1:0] read_addr;
        begin
            @(posedge clk);
            #0.1;
            ren = 1'b1;
            addr = read_addr;
            @(posedge clk);
            #0.1;
            ren = 1'b0;
            // 等待数据有效，需要额外一个周期用于memory读出
            @(posedge clk);
            wait(valid);
            @(posedge clk);
            @(posedge clk);
        end
    endtask
    
    // 连续读取操作的任务
    task continuous_read;
        input [ADDR_WIDTH-1:0] start_addr;
        input integer count;
        integer i;
        begin
            for (i = 0; i < count; i = i + 1) begin
                @(posedge clk);
                #0.1;
                ren = 1'b1;
                addr = start_addr + i;
            end
            @(posedge clk);
            #0.1;
            ren = 1'b0;
            // 等待所有数据都有效，需要额外的周期用于memory读出
            repeat(count + 4) @(posedge clk);
        end
    endtask
    
    // 连续读取相同地址的任务
    task continuous_same_addr;
        input [ADDR_WIDTH-1:0] read_addr;
        input integer count;
        integer i;
        begin
            for (i = 0; i < count; i = i + 1) begin
                @(posedge clk);
                #0.1;
                ren = 1'b1;
                addr = read_addr;
            end
            @(posedge clk);
            #0.1;
            ren = 1'b0;
            // 等待所有数据都有效，需要额外的周期用于memory读出
            repeat(count + 4) @(posedge clk);
        end
    endtask
    
    // 测试主过程
    initial begin
        // 初始化
        clk = 0;
        rst_n = 0;
        ren = 0;
        addr = 0;
        
        // 复位
        #(CLK_PERIOD*2);
        rst_n = 1;
        #(CLK_PERIOD*2);
        
        // 初始化内存
        initialize_memory();
        
        // 测试1: 单次读取和重复单次读取
        $display("\nTest 1: Single reads and repeated single reads");
        read_addr(8'h01);
        check_counter(8'h01, 64'd1);
        read_addr(8'h01);  // 重复读取同一地址
        check_counter(8'h01, 64'd2);
        read_addr(8'h02);  // 读取不同地址
        check_counter(8'h02, 64'd1);
        read_addr(8'h01);  // 再次回到第一个地址
        check_counter(8'h01, 64'd3);
        
        // 测试2: 连续读取不同地址的多种模式
        $display("\nTest 2: Various patterns of continuous reads to different addresses");
        // 2.1: 顺序读取
        continuous_read(8'h03, 3);
        check_counter(8'h03, 64'd1);
        check_counter(8'h04, 64'd1);
        check_counter(8'h05, 64'd1);
        
        // 2.2: 重复顺序读取
        continuous_read(8'h03, 3);
        check_counter(8'h03, 64'd2);
        check_counter(8'h04, 64'd2);
        check_counter(8'h05, 64'd2);
        
        // 2.3: 交错读取
        read_addr(8'h03);
        read_addr(8'h04);
        read_addr(8'h03);
        check_counter(8'h03, 64'd4);
        check_counter(8'h04, 64'd3);
        
        // 测试3: 连续读取相同地址的不同长度
        $display("\nTest 3: Continuous reads to same address with different lengths");
        // 3.1: 短序列
        continuous_same_addr(8'h06, 4);
        check_counter(8'h06, 64'd4);
        
        // 3.2: 中等序列
        continuous_same_addr(8'h06, 8);
        check_counter(8'h06, 64'd12);
        
        // 3.3: 长序列
        continuous_same_addr(8'h06, 16);
        check_counter(8'h06, 64'd28);
        
        // 测试4: 混合读取模式
        $display("\nTest 4: Mixed read patterns");
        // 4.1: 连续读后单次读
        continuous_same_addr(8'h07, 5);
        read_addr(8'h07);
        check_counter(8'h07, 64'd6);
        
        // 4.2: 交替连续读
        continuous_same_addr(8'h08, 3);
        continuous_same_addr(8'h09, 3);
        continuous_same_addr(8'h08, 3);
        check_counter(8'h08, 64'd6);
        check_counter(8'h09, 64'd3);
        
        // 4.3: 复杂混合模式
        read_addr(8'h0A);
        continuous_same_addr(8'h0A, 3);
        read_addr(8'h0B);
        continuous_same_addr(8'h0A, 2);
        check_counter(8'h0A, 64'd6);
        check_counter(8'h0B, 64'd1);
        
        // 测试5: 边界情况和压力测试
        $display("\nTest 5: Edge cases and stress tests");
        // 5.1: 长序列连续读
        continuous_same_addr(8'h0C, 32);
        check_counter(8'h0C, 64'd32);
        
        // 5.2: 快速切换地址
        continuous_read(8'h0D, 1);
        continuous_read(8'h0E, 1);
        continuous_read(8'h0D, 1);
        continuous_read(8'h0E, 1);
        check_counter(8'h0D, 64'd2);
        check_counter(8'h0E, 64'd2);
        
        // 5.3: 重复访问已访问地址
        continuous_same_addr(8'h01, 5);  // 之前已经访问过的地址
        check_counter(8'h01, 64'd8);  // 3 + 5 = 8
        
        // 测试6: 随机访问模式
        $display("\nTest 6: Random access patterns");
        read_addr(8'h01);
        read_addr(8'h05);
        continuous_same_addr(8'h01, 2);
        read_addr(8'h05);
        continuous_same_addr(8'h05, 3);
        check_counter(8'h01, 64'd11);  // 8 + 1 + 2 = 11
        check_counter(8'h05, 64'd7);   // 2 + 2 + 3 = 7
        
        // 测试完成
        #(CLK_PERIOD*5);
        $display("\nAll tests completed");
        $finish;
    end
    
    // 波形输出
    initial begin
        //$dumpfile("mem_with_stat_tb.vcd");
        //$dumpvars(0, memory_counter_optimized_tb);
    end
    
endmodule
