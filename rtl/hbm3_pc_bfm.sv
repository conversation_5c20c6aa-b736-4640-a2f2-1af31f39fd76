`timescale 1ns/1ps
module hbm3_pc_bfm #(
    parameter [9:0] HBM_ID = 0,
    parameter [4:0] CHANNEL_ID = 0,
    parameter [0:0] PC_ID = 0,
    parameter ADDR_WIDTH = 64,
    parameter DATA_WIDTH = 256,
    parameter BASE_ADDR = 64'h0000000000000000,
    parameter PAGE_SIZE = 256*32,     // bits
    parameter PAGE_SWITCH_RATIO = 2,  // When VP_ACCESS * ratio > HW_ACCESS, switch page
    parameter CHANNEL_NUM = 16,
    parameter DPIC_WFIFO_DEPTH = 16,
    parameter DPIC_RFIFO_DEPTH = 16,
    parameter DATA_WIDTH_IN_BYTE = DATA_WIDTH/8,
    parameter CONT_LINE_PER_CHANNEL = 8,  // continuous line per channel
    parameter MERGED_CHANNEL_NUM = 2      // merged channel number
)(
    // Write Port
    input  wire                           mm_clk,
    input  wire                           mm_rstn,
    input  wire                           mm_wen,
    input  wire [64-1:0]                  mm_waddr,    // this is the system address
    input  wire [DATA_WIDTH-1:0]          mm_wdata,
    input  wire [DATA_WIDTH_IN_BYTE-1:0]  mm_wmask,
    
    // Read Port
    input  wire                           mm_ren,
    input  wire [64-1:0]                  mm_raddr,    // this is the system address
    output reg  [DATA_WIDTH-1:0]          mm_rdata,
    output reg                            mm_rvalid
);

    localparam [15:0] INST_ID = {PC_ID, CHANNEL_ID, HBM_ID};
    localparam [63:0] MEM_DEPTH =  1<<ADDR_WIDTH;
    localparam [63:0] PAGE_NUM =  MEM_DEPTH * DATA_WIDTH / PAGE_SIZE;

    localparam PAGE_SIZE_AWIDTH = $clog2(PAGE_SIZE/DATA_WIDTH);
    localparam DATA_WIDTH_BYTES_AWIDTH = $clog2(DATA_WIDTH_IN_BYTE);
    localparam DATA_TO_PAGE_OFFSET = PAGE_SIZE_AWIDTH - DATA_WIDTH_BYTES_AWIDTH;


    localparam ST_RTL_READ_IDLE = 0;
    localparam ST_RTL_READ_ADDR = 1;
    localparam ST_RTL_READ_DONE = 2;

    localparam ST_RTL_WRITE_IDLE = 0;
    localparam ST_RTL_WRITE_ADDR = 1;
    localparam ST_RTL_WRITE_DONE = 2;

    localparam DPIC_WFIFO_AWIDTH = $clog2(DPIC_WFIFO_DEPTH);
    localparam DPIC_RFIFO_AWIDTH = $clog2(DPIC_RFIFO_DEPTH);

    localparam ST_DPI_WRITE_IDLE = 0;
    localparam ST_DPI_WRITE_ADDR = 1;
    localparam ST_DPI_WRITE_DATA = 2;
    localparam ST_DPI_WRITE_DONE = 3;

    localparam ST_DPI_READ_IDLE = 0;
    localparam ST_DPI_READ_ADDR = 1;
    localparam ST_DPI_READ_DATA = 2;
    localparam ST_DPI_READ_DONE = 3;


`ifdef VP_FAKE_SYNC_TEST
import "DPI-C" context function void h2s_write_test(int unsigned id, bit [64-1:0] addr, bit [DATA_WIDTH-1:0] data, bit [DATA_WIDTH_IN_BYTE-1:0] mask);
import "DPI-C" context function void h2s_read_test(int unsigned id, bit [64-1:0] addr);
import "DPI-C" context function void h2s_read_resp(int unsigned id, bit [64-1:0] addr, output bit [DATA_WIDTH-1:0] data);
`endif

    // Internal registers and memories
    reg [63:0] page_table[0:PAGE_NUM-1];  // [63]: in_hw, [62]: written, [61:31]: rtl_write_count, [30:0]: rtl_read_count
    reg [DATA_WIDTH-1:0] memory [0:MEM_DEPTH-1]; // system view

    bit write_priority=0;
    bit write_conflict=0;
    bit write_is_done=0;

    bit prev_resetn=1;

    function automatic bit [63:0] to_offset_addr(
        input bit [9:0] hbm_id,
        input bit [4:0] channel_id,
        input bit [0:0] pc_id,
        input bit [63-1:0] addr
    );
    bit [63:0] offset_addr;
    begin
        offset_addr = (addr - BASE_ADDR ) >> DATA_WIDTH_BYTES_AWIDTH;
        return CONT_LINE_PER_CHANNEL * MERGED_CHANNEL_NUM * (offset_addr/ (CHANNEL_NUM*CONT_LINE_PER_CHANNEL)) + (offset_addr) % (CONT_LINE_PER_CHANNEL*MERGED_CHANNEL_NUM);
    end
    endfunction

    function automatic bit is_legal_addr(
        input bit [4:0] channel_id,
        input bit [63:0] addr
    );
    bit [63:0] offset_addr;
    bit [4:0] actual_channel_id;
    begin
        if ((addr <= BASE_ADDR) || (addr > BASE_ADDR + MEM_DEPTH*4)) begin
            return 0;
        end
        offset_addr = (addr - BASE_ADDR ) >> DATA_WIDTH_BYTES_AWIDTH;
        actual_channel_id = (offset_addr / 16) % CHANNEL_NUM;
        if ((channel_id == actual_channel_id) || ((channel_id+1) == actual_channel_id)) begin
            return 1;
        end else begin
            return 0;
        end
    end
    endfunction

    // convert offset address to page id
    function automatic bit [63:0] to_page_id(
        input bit [63:0] offset_addr
    );
    bit [63:0] page_id;
    begin
        page_id = offset_addr >> PAGE_SIZE_AWIDTH;
    end
    endfunction

`ifdef VP_FAKE_SYNC_TEST
    function void start_write_test(bit [63:0] addr, bit [DATA_WIDTH-1:0] data, bit [DATA_WIDTH_IN_BYTE-1:0] mask);
        $display("[SV] Start C write test called with id=%d", INST_ID);
        h2s_write_test(INST_ID, addr, data, mask);
        $display("[SV] End C write test called with id=%d", INST_ID);
    endfunction
    function void start_read_test(bit [63:0] addr);
        $display("[SV] Start C read test called with id=%d", INST_ID);
        h2s_read_test(INST_ID, addr);
        $display("[SV] End C read test called with id=%d", INST_ID);
    endfunction
    function bit [DATA_WIDTH-1:0] start_read_back(bit [63:0] addr);
        $display("[SV] Read back data called with id=%d", INST_ID);
        h2s_read_resp(INST_ID, addr, start_read_back);
        $display("[SV] End Read back data called with id=%d", INST_ID);
    endfunction
`endif

    // read and write is done
    //bit rtl_read_is_clean;
    //bit rtl_write_is_clean;
    bit wdone;

    // Pipeline registers for read operations
    localparam PIPELINE_LEVEL=3;
    reg [ADDR_WIDTH-1:0] read_pipe_addr[0:PIPELINE_LEVEL-1];  
    reg [63:0] read_pipe_sys_addr[0:PIPELINE_LEVEL-1];  
    reg [63:0] read_pipe_offset_addr[0:PIPELINE_LEVEL-1];  
    reg [63:0] read_pipe_page_item[0:PIPELINE_LEVEL-1];  
    reg [DATA_WIDTH-1:0] read_pipe_data[0:PIPELINE_LEVEL-1];  
    reg read_pipe_valid[0:PIPELINE_LEVEL-1];  

    // Pipeline registers for write operations
    reg [ADDR_WIDTH-1:0] write_pipe_addr[0:PIPELINE_LEVEL-1];  
    reg [63:0] write_pipe_sys_addr[0:PIPELINE_LEVEL-1];  
    reg [63:0] write_pipe_offset_addr[0:PIPELINE_LEVEL-1];  
    reg [63:0] write_pipe_page_item[0:PIPELINE_LEVEL-1];  
    reg [DATA_WIDTH-1:0] write_pipe_data[0:PIPELINE_LEVEL-1];  
    reg [DATA_WIDTH_IN_BYTE-1:0] write_pipe_mask[0:PIPELINE_LEVEL-1];  
    reg write_pipe_valid[0:PIPELINE_LEVEL-1];  

    // notify registers
    bit [31:0] rtl_notify_interval;   // Configurable notification interval

    // DPI-C write address
    bit [63:0] dpi_write_sys_addr;
    bit [63:0] dpi_write_offset_addr;

    // DPI-C interface declarations
    // 0: pc_id,  1-5: channel_id, 6-15: hbm_id
    import "DPI-C" context function void init_bfm(
        input shortint unsigned id,
        input int unsigned page_size_in_bit,
        input longint unsigned addr_width,
        input shortint unsigned data_width,
        input int unsigned cont_line_per_channel,
        input byte merged_channel_num
    );

    import "DPI-C" context function void reset_page_table(
        input bit [15:0] id
    );

    import "DPI-C" context function void h2s_pc_write(
        input bit [15:0] id,
        input bit [64-1:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input int unsigned data_size_in_bits,
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask,
        output bit done
    );

    import "DPI-C" context function void h2s_pc_read(
        input bit [15:0] id,
        input bit [64-1:0] addr,
        input int unsigned data_size_in_bits,
        output bit [DATA_WIDTH-1:0] data
    );

    (* is_nonblocking_dpi=1 *)
    import "DPI-C" context function void h2s_notify_page_read_count(
        input bit [63:0] page_id,
        input bit [31:0] access_count
    );

    (* is_nonblocking_dpi=1 *)
    import "DPI-C" context function void h2s_notify_page_write_count(
        input bit [63:0] page_id,
        input bit [31:0] access_count
    );

    // Pipeline advancement logic
    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            // Reset all pipeline registers
            for (int i = 0; i < PIPELINE_LEVEL; i++) begin
                read_pipe_valid[i] <= 0;
                write_pipe_valid[i] <= 0;
            end
        end else begin
            // Read pipeline advancement
            for (int i = PIPELINE_LEVEL-1; i > 0; i--) begin
                read_pipe_addr[i] <= read_pipe_addr[i-1];
                read_pipe_sys_addr[i] <= read_pipe_sys_addr[i-1];
                read_pipe_offset_addr[i] <= read_pipe_offset_addr[i-1];
                //read_pipe_page_item[i] <= read_pipe_page_item[i-1];
                //read_pipe_data[i] <= read_pipe_data[i-1];
                read_pipe_valid[i] <= read_pipe_valid[i-1];
            end
            for (int i = PIPELINE_LEVEL-1; i > 1; i--) begin
                read_pipe_page_item[i] <= read_pipe_page_item[i-1];
            end
            

            // Write pipeline advancement
            for (int i = PIPELINE_LEVEL-1; i > 0; i--) begin
                write_pipe_addr[i] <= write_pipe_addr[i-1];
                write_pipe_sys_addr[i] <= write_pipe_sys_addr[i-1];
                write_pipe_offset_addr[i] <= write_pipe_offset_addr[i-1];
                //write_pipe_page_item[i] <= write_pipe_page_item[i-1];
                write_pipe_data[i] <= write_pipe_data[i-1];
                write_pipe_mask[i] <= write_pipe_mask[i-1];
                write_pipe_valid[i] <= write_pipe_valid[i-1];
            end
            for (int i = PIPELINE_LEVEL-1; i > 1; i--) begin
                write_pipe_page_item[i] <= write_pipe_page_item[i-1];
            end
            
            // New requests enter pipeline
            if (mm_ren) begin
                read_pipe_sys_addr[0] <= mm_raddr;
                read_pipe_offset_addr[0] <= to_offset_addr(HBM_ID, CHANNEL_ID, PC_ID, mm_raddr);
                read_pipe_valid[0] <= 1;
            end else begin
                read_pipe_valid[0] <= 0;
            end

            if (mm_wen) begin
                write_pipe_sys_addr[0] <= mm_waddr;
                write_pipe_offset_addr[0] <= to_offset_addr(HBM_ID, CHANNEL_ID, PC_ID, mm_waddr);
                write_pipe_data[0] <= mm_wdata;
                write_pipe_mask[0] <= mm_wmask;
                write_pipe_valid[0] <= 1;
            end else begin
                write_pipe_valid[0] <= 0;
            end
        end
    end

    // Stage 1: Page table lookup
    always @(posedge mm_clk) begin
        if (read_pipe_valid[0]) begin
            read_pipe_page_item[1] <= page_table[read_pipe_offset_addr[0][63:PAGE_SIZE_AWIDTH]];
        end
        if (write_pipe_valid[0]) begin
            write_pipe_page_item[1] <= page_table[write_pipe_offset_addr[0][63:PAGE_SIZE_AWIDTH]];
        end
    end

    wire [30:0] read_pipe_page_item_write_count;
    wire [30:0] read_pipe_page_item_read_count;

    assign read_pipe_page_item_write_count = read_pipe_page_item[1][61:31];
    assign read_pipe_page_item_read_count = read_pipe_page_item[1][30:0];

    // Stage 2: Memory access, update page table, notify the page access count
    always @(posedge mm_clk) begin
        // read
        if (read_pipe_valid[1]) begin
            if (read_pipe_page_item[1][63]) begin // Page in HW
                read_pipe_data[2] <= memory[read_pipe_offset_addr[1]];

                // update page table
                if ((read_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH] === write_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH]) && ( write_pipe_valid[1])) begin
                    page_table[read_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH]] <= {read_pipe_page_item[1][63], 1'b1, read_pipe_page_item_write_count+31'd1, read_pipe_page_item_read_count+31'd1};
                end else begin
                    page_table[read_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH]] <= {read_pipe_page_item[1][63], 1'b1, read_pipe_page_item_write_count, read_pipe_page_item_read_count+31'd1};
                end

                // notify the page access count
                if ((read_pipe_page_item[1][61:31]+32'h1) % rtl_notify_interval == 0) begin
                    h2s_notify_page_read_count(read_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH], read_pipe_page_item[1][61:31]+32'h1);
                end
            end else begin // Page in VP
                h2s_pc_read(INST_ID, read_pipe_sys_addr[1], DATA_WIDTH, read_pipe_data[2]);
            end
        end
        
        // write
        if (write_pipe_valid[1]) begin
            if (write_pipe_page_item[1][63]) begin // Page in HW
                for (int i = 0; i < DATA_WIDTH_IN_BYTE; i++) begin
                    if (write_pipe_mask[1][i]) begin
                        memory[write_pipe_offset_addr[1]][i*8 +: 8] <= write_pipe_data[1][i*8 +: 8];
                    end
                end

                // update page table
                if ((write_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH] !== read_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH]) || ( !read_pipe_valid[1])) begin
                    page_table[write_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH]] <= {write_pipe_page_item[1][63], 1'b1, write_pipe_page_item[1][61:31]+31'h1, write_pipe_page_item[1][30:0]};
                end

                // notify the page access count
                if ((write_pipe_page_item[1][61:31]+1) % rtl_notify_interval == 0) begin
                    h2s_notify_page_write_count(write_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH], write_pipe_page_item[1][61:31]+32'h1);
                end
            end else begin // Page in VP
                h2s_pc_write(INST_ID, write_pipe_sys_addr[1], write_pipe_data[1], DATA_WIDTH, write_pipe_mask[1], wdone);
            end
        end
    end

    // Stage 3: Response handling
    always @(posedge mm_clk) begin
        if (read_pipe_valid[2]) begin
            mm_rdata <= read_pipe_data[2];
            mm_rvalid <= 1;
        end else begin
            mm_rvalid <= 0;
        end
        
        if (write_pipe_valid[2]) begin
            // Handle write response if needed
            write_is_done <= 1'b1;
        end else begin
            write_is_done <= 1'b0;
        end
    end

// the interface for VP to access the data in the HW

`ifdef VP_TEST
    import "DPI-C" context function void h2s_polling_dpi_read();
    import "DPI-C" context function void h2s_polling_dpi_write();
    reg [DPIC_RFIFO_AWIDTH:0] read_polling_send_count;
    reg [DPIC_RFIFO_AWIDTH:0] read_polling_recv_count;
    reg [DPIC_WFIFO_AWIDTH:0] write_polling_send_count;
    reg [DPIC_WFIFO_AWIDTH:0] write_polling_recv_count;
    reg [31:0] read_polling_count;
    reg [31:0] read_polling_interval;
    reg [31:0] write_polling_count;
    reg [31:0] write_polling_interval;
`endif

    // Export DPI-C functions for VP access
    export "DPI-C" function s2h_pc_write_req;
    export "DPI-C" function s2h_pc_read_req;

    reg [DATA_WIDTH-1:0] dpi_wdata_fifo [0: DPIC_WFIFO_DEPTH-1];
    reg [64-1:0] dpi_waddr_fifo [0: DPIC_WFIFO_DEPTH-1];
    reg [DATA_WIDTH_IN_BYTE-1:0] dpi_wmask_fifo [0: DPIC_WFIFO_DEPTH-1];

    reg [64-1:0] dpi_raddr_fifo [0: DPIC_RFIFO_DEPTH-1];

    reg [DPIC_WFIFO_AWIDTH:0] dpi_wfifo_rptr;
    reg [DPIC_WFIFO_AWIDTH:0] dpi_wfifo_wptr;
    reg [DPIC_RFIFO_AWIDTH:0] dpi_rfifo_rptr;
    reg [DPIC_RFIFO_AWIDTH:0] dpi_rfifo_wptr;

    bit [DPIC_WFIFO_AWIDTH:0] dpi_wleft_space;
    bit [DPIC_RFIFO_AWIDTH:0] dpi_rleft_space;
    bit dpi_wfifo_empty;
    bit dpi_rfifo_empty;

    // Pipeline registers for DPI access
    // Stage 1: Request
    localparam DPI_PIPELINE_LEVEL = 2;
    reg [63:0] dpi_read_sys_addr_pipe[0:DPI_PIPELINE_LEVEL-1];
    reg [63:0] dpi_read_offset_addr_pipe[0:DPI_PIPELINE_LEVEL-1];
    reg [63:0] dpi_write_sys_addr_pipe[0:DPI_PIPELINE_LEVEL-1];
    reg [63:0] dpi_write_offset_addr_pipe[0:DPI_PIPELINE_LEVEL-1];
    reg [DATA_WIDTH-1:0] dpi_write_data_pipe[0:DPI_PIPELINE_LEVEL-1];
    reg [DATA_WIDTH_IN_BYTE-1:0] dpi_write_mask_pipe[0:DPI_PIPELINE_LEVEL-1];
    reg dpi_read_valid_pipe[0:DPI_PIPELINE_LEVEL-1];
    reg dpi_write_valid_pipe[0:DPI_PIPELINE_LEVEL-1];

    // Stage 2: Memory
    reg [DATA_WIDTH-1:0] dpi_read_data_pipe[1:2];

    // Stage 3: Response
    reg [DATA_WIDTH-1:0] dpi_read_data;

    // Remote request handling functions
`ifdef VP_TEST
    function automatic int s2h_pc_write_req(
        input bit [64-1:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input int unsigned data_size_in_bits,
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask,
        input byte valid
    );
    begin
        write_polling_recv_count = write_polling_recv_count + 1;
        if (valid == 1) begin
            if (dpi_wleft_space > 0) begin
                dpi_wdata_fifo[dpi_wfifo_wptr[DPIC_WFIFO_AWIDTH-1:0]] = data;
                dpi_waddr_fifo[dpi_wfifo_wptr[DPIC_WFIFO_AWIDTH-1:0]] = addr;
                dpi_wmask_fifo[dpi_wfifo_wptr[DPIC_WFIFO_AWIDTH-1:0]] = mask;
                if (mm_rstn) begin
                    dpi_wfifo_wptr = dpi_wfifo_wptr + 1;
                end else begin
                    dpi_wfifo_wptr = 0;
                end
                return 1;
            end else begin
                return 0;
            end
        end
    end
    endfunction
`else
    function automatic int s2h_pc_write_req(
        input bit [64-1:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input int unsigned data_size_in_bits,
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask
    );
    begin
        if (dpi_wleft_space > 0) begin
            dpi_wdata_fifo[dpi_wfifo_wptr[DPIC_WFIFO_AWIDTH-1:0]] = data;
            dpi_waddr_fifo[dpi_wfifo_wptr[DPIC_WFIFO_AWIDTH-1:0]] = addr;
            dpi_wmask_fifo[dpi_wfifo_wptr[DPIC_WFIFO_AWIDTH-1:0]] = mask;
            if (mm_rstn) begin
                dpi_wfifo_wptr = dpi_wfifo_wptr + 1;
            end else begin
                dpi_wfifo_wptr = 0;
            end
            return 1;
        end else begin
            return 0;
        end
    end
    endfunction
`endif

`ifdef VP_TEST
    function automatic int s2h_pc_read_req(
        input bit [64-1:0] addr,
        input int unsigned data_size_in_bits,
        input byte valid
    );
    begin
        read_polling_recv_count = read_polling_recv_count + 1;
        `ifdef DEBUG
        //$display("[SV] s2h_pc_read_req is called at %t", $time);
        `endif
        if (valid == 1) begin
            if (dpi_rleft_space > 0) begin
                dpi_raddr_fifo[dpi_rfifo_wptr[DPIC_RFIFO_AWIDTH-1:0]] = addr;
                if (mm_rstn) begin
                    dpi_rfifo_wptr = dpi_rfifo_wptr + 1;
                end else begin
                    dpi_rfifo_wptr = 0;
                end
                `ifdef DEBUG
                //$display("[SV] dpi_rfifo_wptr is %d, dpi_rfifo_rptr is %d", dpi_rfifo_wptr, dpi_rfifo_rptr);
                `endif
                return 1;
            end else begin
                return 0;
            end
        end else begin
            return 0;
        end
    end
    endfunction
`else
    function automatic int s2h_pc_read_req(
        input bit [64-1:0] addr,
        input int unsigned data_size_in_bits
    );
    begin
        `ifdef DEBUG
        //$display("[SV] s2h_pc_read_req is called at %t", $time);
        `endif
        if (dpi_rleft_space > 0) begin
            dpi_raddr_fifo[dpi_rfifo_wptr[DPIC_RFIFO_AWIDTH-1:0]] = addr;
            if (mm_rstn) begin
                dpi_rfifo_wptr = dpi_rfifo_wptr + 1;
            end else begin
                dpi_rfifo_wptr = 0;
            end
            `ifdef DEBUG
            //$display("[SV] dpi_rfifo_wptr is %d, dpi_rfifo_rptr is %d", dpi_rfifo_wptr, dpi_rfifo_rptr);
            `endif
            return 1;
        end else begin
            return 0;
        end
    end
    endfunction
`endif

    (* is_blocking_dpi=1 *)
    import "DPI-C" context function void h2s_pc_write_resp(
        input bit [15:0] id,
        input bit [64-1:0] addr,
        input bit write_conflict
    );

    (* is_nonblocking_dpi=1 *)
    import "DPI-C" context function void h2s_pc_read_resp(
        input bit [15:0] id,
        input bit [64-1:0] addr,
        input int unsigned data_size_in_bits,
        input bit [DATA_WIDTH-1:0] data
    );

    function automatic [DPIC_WFIFO_AWIDTH:0] calculate_wfifo_space(
        input [DPIC_WFIFO_AWIDTH:0] rptr,
        input [DPIC_WFIFO_AWIDTH:0] wptr
    );
        if (rptr[DPIC_WFIFO_AWIDTH] == ~wptr[DPIC_WFIFO_AWIDTH]) begin
            calculate_wfifo_space = rptr[DPIC_WFIFO_AWIDTH-1:0] - wptr[DPIC_WFIFO_AWIDTH-1:0];
        end else begin
            calculate_wfifo_space = DPIC_WFIFO_DEPTH - wptr[DPIC_WFIFO_AWIDTH-1:0] + rptr[DPIC_WFIFO_AWIDTH-1:0];
        end
    endfunction

`ifdef VP_TEST
    function automatic [DPIC_WFIFO_AWIDTH:0] calculate_wreq_number(
        input [DPIC_WFIFO_AWIDTH:0] send,
        input [DPIC_WFIFO_AWIDTH:0] recv
    );
        if (send[DPIC_WFIFO_AWIDTH] == recv[DPIC_WFIFO_AWIDTH]) begin
            calculate_wreq_number = send[DPIC_WFIFO_AWIDTH-1:0] - recv[DPIC_WFIFO_AWIDTH-1:0];
        end else begin
            calculate_wreq_number = DPIC_WFIFO_DEPTH - recv[DPIC_WFIFO_AWIDTH-1:0] + send[DPIC_WFIFO_AWIDTH-1:0];
        end
    endfunction

    function automatic [DPIC_WFIFO_AWIDTH:0] calculate_wfifo_safe_space(
        input [DPIC_WFIFO_AWIDTH:0] rptr,
        input [DPIC_WFIFO_AWIDTH:0] wptr,
        input [DPIC_WFIFO_AWIDTH:0] onroad_num
    );
        if (rptr[DPIC_WFIFO_AWIDTH] == ~wptr[DPIC_WFIFO_AWIDTH]) begin
            calculate_wfifo_safe_space = rptr[DPIC_WFIFO_AWIDTH-1:0] - wptr[DPIC_WFIFO_AWIDTH-1:0] - onroad_num;
        end else begin
            calculate_wfifo_safe_space = DPIC_WFIFO_DEPTH - wptr[DPIC_WFIFO_AWIDTH-1:0] + rptr[DPIC_WFIFO_AWIDTH-1:0] - onroad_num;
        end
    endfunction
`endif

    function automatic [DPIC_RFIFO_AWIDTH:0] calculate_rfifo_space(
        input [DPIC_RFIFO_AWIDTH:0] rptr,
        input [DPIC_RFIFO_AWIDTH:0] wptr
    );
        if (rptr[DPIC_RFIFO_AWIDTH] == ~wptr[DPIC_RFIFO_AWIDTH]) begin
            calculate_rfifo_space = rptr[DPIC_RFIFO_AWIDTH-1:0] - wptr[DPIC_RFIFO_AWIDTH-1:0];
        end else begin
            calculate_rfifo_space = DPIC_RFIFO_DEPTH - wptr[DPIC_RFIFO_AWIDTH-1:0] + rptr[DPIC_RFIFO_AWIDTH-1:0];
        end
    endfunction

`ifdef VP_TEST
    function automatic [DPIC_RFIFO_AWIDTH:0] calculate_rreq_number(
        input [DPIC_RFIFO_AWIDTH:0] send,
        input [DPIC_RFIFO_AWIDTH:0] recv
    );
        if (send[DPIC_RFIFO_AWIDTH] == recv[DPIC_RFIFO_AWIDTH]) begin
            calculate_rreq_number = send[DPIC_RFIFO_AWIDTH-1:0] - recv[DPIC_RFIFO_AWIDTH-1:0];
        end else begin
            calculate_rreq_number = DPIC_RFIFO_DEPTH - recv[DPIC_RFIFO_AWIDTH-1:0] + send[DPIC_RFIFO_AWIDTH-1:0];
        end
    endfunction

    function automatic [DPIC_RFIFO_AWIDTH:0] calculate_rfifo_safe_space(
        input [DPIC_RFIFO_AWIDTH:0] rptr,
        input [DPIC_RFIFO_AWIDTH:0] wptr,
        input [DPIC_RFIFO_AWIDTH:0] onroad_num
    );
        if (rptr[DPIC_RFIFO_AWIDTH] == ~wptr[DPIC_RFIFO_AWIDTH]) begin
            calculate_rfifo_safe_space = rptr[DPIC_RFIFO_AWIDTH-1:0] - wptr[DPIC_RFIFO_AWIDTH-1:0] - onroad_num;
        end else begin
            calculate_rfifo_safe_space = DPIC_RFIFO_DEPTH - wptr[DPIC_RFIFO_AWIDTH-1:0] + rptr[DPIC_RFIFO_AWIDTH-1:0] - onroad_num;
        end
    endfunction
`endif

    // FIFO status check
    //assign dpi_wfifo_empty = (dpi_wfifo_rptr === dpi_wfifo_wptr);
    //assign dpi_rfifo_empty = (dpi_rfifo_rptr === dpi_rfifo_wptr);
    //assign dpi_wleft_space = calculate_wfifo_space(dpi_wfifo_rptr, dpi_wfifo_wptr);
    //assign dpi_rleft_space = calculate_rfifo_space(dpi_rfifo_rptr, dpi_rfifo_wptr);

    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            dpi_wfifo_empty <= 1;
            dpi_rfifo_empty <= 1;
        end else begin
            dpi_wfifo_empty <= (dpi_wfifo_rptr === dpi_wfifo_wptr);
            dpi_rfifo_empty <= (dpi_rfifo_rptr === dpi_rfifo_wptr);
            dpi_wleft_space <= calculate_wfifo_space(dpi_wfifo_rptr, dpi_wfifo_wptr);
            dpi_rleft_space <= calculate_rfifo_space(dpi_rfifo_rptr, dpi_rfifo_wptr);
        end
    end

    // Handle VP-side access with pipeline
    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            dpi_rfifo_rptr <= dpi_rfifo_wptr;
            dpi_wfifo_rptr <= dpi_wfifo_wptr;
            for (int i = 0; i < 3; i++) begin
                dpi_read_valid_pipe[i] <= 0;
                dpi_write_valid_pipe[i] <= 0;
            end
        end else begin
            // Stage 1: Request
            // Read request
            if (dpi_rfifo_rptr != dpi_rfifo_wptr) begin
                dpi_read_sys_addr_pipe[0] <= dpi_raddr_fifo[dpi_rfifo_rptr[DPIC_RFIFO_AWIDTH-1:0]];
                dpi_read_offset_addr_pipe[0] <= to_offset_addr(HBM_ID, CHANNEL_ID, PC_ID, dpi_raddr_fifo[dpi_rfifo_rptr[DPIC_RFIFO_AWIDTH-1:0]]);
                dpi_rfifo_rptr <= dpi_rfifo_rptr + 1;
                dpi_read_valid_pipe[0] <= 1;
            end else begin
                dpi_read_valid_pipe[0] <= 0;
            end

            // Write request
            if (dpi_wfifo_rptr != dpi_wfifo_wptr) begin
                dpi_write_sys_addr_pipe[0] <= dpi_waddr_fifo[dpi_wfifo_rptr[DPIC_WFIFO_AWIDTH-1:0]];
                dpi_write_offset_addr_pipe[0] <= to_offset_addr(HBM_ID, CHANNEL_ID, PC_ID, dpi_waddr_fifo[dpi_wfifo_rptr[DPIC_WFIFO_AWIDTH-1:0]]);
                dpi_write_data_pipe[0] <= dpi_wdata_fifo[dpi_wfifo_rptr[DPIC_WFIFO_AWIDTH-1:0]];
                dpi_write_mask_pipe[0] <= dpi_wmask_fifo[dpi_wfifo_rptr[DPIC_WFIFO_AWIDTH-1:0]];
                dpi_write_valid_pipe[0] <= 1;
                dpi_wfifo_rptr <= dpi_wfifo_rptr + 1;
            end else begin
                dpi_write_valid_pipe[0] <= 0;
            end

            // Pipeline advancement
            for (int i = 1; i < DPI_PIPELINE_LEVEL; i++) begin
                dpi_read_sys_addr_pipe[i] <= dpi_read_sys_addr_pipe[i-1];
                dpi_read_offset_addr_pipe[i] <= dpi_read_offset_addr_pipe[i-1];
                dpi_read_valid_pipe[i] <= dpi_read_valid_pipe[i-1];
                dpi_write_sys_addr_pipe[i] <= dpi_write_sys_addr_pipe[i-1];
                dpi_write_offset_addr_pipe[i] <= dpi_write_offset_addr_pipe[i-1];
                dpi_write_data_pipe[i] <= dpi_write_data_pipe[i-1];
                dpi_write_mask_pipe[i] <= dpi_write_mask_pipe[i-1];
                dpi_write_valid_pipe[i] <= dpi_write_valid_pipe[i-1];
            end

            // Stage 2: Memory
            // Read memory
            if (dpi_read_valid_pipe[0]) begin
                dpi_read_data_pipe[1] <= memory[dpi_read_offset_addr_pipe[0]];
            end

            // Write memory
            if (dpi_write_valid_pipe[0]) begin
                if (write_pipe_valid[1] && write_pipe_sys_addr[1] == dpi_write_sys_addr_pipe[1] && !write_priority) begin
                    write_conflict <= 1;
                end else begin
                    for (int i = 0; i < DATA_WIDTH_IN_BYTE; i++) begin
                        if (dpi_write_mask_pipe[0][i]) begin
                            memory[dpi_write_offset_addr_pipe[0]][i*8 +: 8] <= dpi_write_data_pipe[0][i*8 +: 8];
                        end
                    end
                end
            end

            // Pipeline advancement
            //dpi_read_data_pipe[2] <= dpi_read_data_pipe[1];

            // Stage 3: Response
            // Read response
            if (dpi_read_valid_pipe[1]) begin
                dpi_read_data <= dpi_read_data_pipe[1];
                h2s_pc_read_resp(
                    INST_ID,
                    dpi_read_sys_addr_pipe[1],
                    DATA_WIDTH,
                    dpi_read_data_pipe[1]
                );
            end

            // Write response
            if (dpi_write_valid_pipe[1]) begin
                h2s_pc_write_resp(
                    INST_ID,
                    dpi_write_sys_addr_pipe[1],
                    write_conflict
                );
                write_conflict <= 0;
            end
        end
    end

`ifdef VP_TEST
    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            read_polling_send_count <= read_polling_recv_count;
            write_polling_send_count <= write_polling_recv_count;
            read_polling_count <= 0;
            write_polling_count <= 0;
        end else begin
            if (read_polling_count < read_polling_interval) begin
                read_polling_count <= read_polling_count + 1;
            end else begin
                read_polling_count <= 0;
            end
            if (write_polling_count < write_polling_interval) begin
                write_polling_count <= write_polling_count + 1;
            end else begin
                write_polling_count <= 0;
            end
            if ((calculate_rfifo_safe_space(dpi_rfifo_rptr, dpi_rfifo_wptr, calculate_rreq_number(read_polling_send_count, read_polling_recv_count)) > 0)&&(read_polling_count == read_polling_interval)) begin
                //$display("%t: polling read request", $time);
                h2s_polling_dpi_read();
                read_polling_send_count <= read_polling_send_count + 1;
            end
            if ((calculate_wfifo_safe_space(dpi_wfifo_rptr, dpi_wfifo_wptr, calculate_wreq_number(write_polling_send_count, write_polling_recv_count)) > 0) && (write_polling_count == write_polling_interval)) begin
                //$display("%t: polling write request", $time);
                h2s_polling_dpi_write();
                write_polling_send_count <= write_polling_send_count + 1;
            end
        end
        
    end
`endif

    // no state request handling
    // similar to page switch handling

`ifdef NO_STATE_INTERFACE
    import "DPI-C" context function void h2s_no_state_response(int request_id);
    export "DPI-C" function h2s_no_state_request;

    localparam NO_STATE_FIFO_DEPTH = 16;
    localparam NO_STATE_FIFO_AWIDTH = $clog2(NO_STATE_FIFO_DEPTH);
    localparam NO_STATE_INIT = 0;
    localparam NO_STATE_GET = 1;
    localparam NO_STATE_WAIT = 2;
    localparam NO_STATE_CLEAN = 3;
    localparam NO_STATE_DONE = 4;

    bit [NO_STATE_FIFO_AWIDTH:0] no_state_fifo_rptr;
    bit [NO_STATE_FIFO_AWIDTH:0] no_state_fifo_wptr;
    bit [NO_STATE_FIFO_AWIDTH:0] no_state_fifo_left_space;
    bit no_state_fifo_empty;

    bit [31:0] no_state_fifo [0: NO_STATE_FIFO_DEPTH-1];
    bit no_state_valid;
    bit [31:0] no_state_id;
    bit [2:0] current_no_state_state;
    bit [2:0] next_no_state_state;


    function automatic [NO_STATE_FIFO_AWIDTH:0] calculate_no_state_fifo_space(
        input [NO_STATE_FIFO_AWIDTH:0] rptr,
        input [NO_STATE_FIFO_AWIDTH:0] wptr
    );
        if (rptr[NO_STATE_FIFO_AWIDTH] == ~wptr[NO_STATE_FIFO_AWIDTH]) begin
            calculate_no_state_fifo_space = rptr[NO_STATE_FIFO_AWIDTH-1:0] - wptr[NO_STATE_FIFO_AWIDTH-1:0];
        end else begin
            calculate_no_state_fifo_space = NO_STATE_FIFO_DEPTH - wptr[NO_STATE_FIFO_AWIDTH-1:0] + rptr[NO_STATE_FIFO_AWIDTH-1:0];
        end
    endfunction

    function automatic int h2s_no_state_request(input int request_id);
        if (no_state_fifo_left_space > 0) begin
            no_state_fifo[no_state_fifo_wptr[NO_STATE_FIFO_AWIDTH-1:0]] = request_id;
            no_state_fifo_wptr = no_state_fifo_wptr + 1;
            return 1;
        end else begin
            return 0;
        end
    endfunction

    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            no_state_fifo_empty <= 1;
            no_state_fifo_left_space <= NO_STATE_FIFO_DEPTH;
            current_no_state_state <= NO_STATE_INIT;
        end else begin
            no_state_fifo_empty <= (no_state_fifo_rptr == no_state_fifo_wptr);
            no_state_fifo_left_space <= calculate_no_state_fifo_space(no_state_fifo_rptr, no_state_fifo_wptr);
            current_no_state_state <= next_no_state_state;
        end
    end

    always @(*) begin
        case(current_no_state_state)
            NO_STATE_INIT:
                if (no_state_fifo_rptr != no_state_fifo_wptr) begin
                    next_no_state_state = NO_STATE_GET;
                end else begin
                    next_no_state_state = NO_STATE_INIT;
                end
            NO_STATE_GET:
                next_no_state_state = NO_STATE_WAIT;
            NO_STATE_WAIT:
                if (dpi_rfifo_empty && dpi_wfifo_empty && !dpi_read_valid_pipe[0] && !dpi_write_valid_pipe[0]
                && !dpi_read_valid_pipe[1] && !dpi_write_valid_pipe[1]) begin
                    next_no_state_state = NO_STATE_CLEAN;
                end else begin
                    next_no_state_state = NO_STATE_WAIT;
                end
            NO_STATE_CLEAN:
                if (no_state_fifo_rptr != no_state_fifo_wptr) begin
                    next_no_state_state = NO_STATE_WAIT;
                end else begin
                    next_no_state_state = NO_STATE_INIT;
                end
            default:
                next_no_state_state = NO_STATE_INIT;
        endcase
    end

    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            no_state_fifo_rptr <= no_state_fifo_wptr;
            no_state_valid <= 0;
        end else begin
            case(current_no_state_state)
                NO_STATE_INIT:
                begin
                    no_state_valid <= 0;
                end
                NO_STATE_GET:
                begin
                    no_state_id = no_state_fifo[no_state_fifo_rptr[NO_STATE_FIFO_AWIDTH-1:0]];
                    no_state_fifo_rptr <= no_state_fifo_rptr + 1;
                    no_state_valid <= 1;
                end
                NO_STATE_WAIT:
                begin
                end
                NO_STATE_CLEAN:
                begin
                    h2s_no_state_response(no_state_id);
                    no_state_valid <= 0;
                end
            endcase
        end
    end
`endif

    // Page switch handling

    import "DPI-C" context function void h2s_switch_page_response();
    export "DPI-C" function h2s_switch_page_request;

    localparam PAGE_SWITCH_FIFO_DEPTH = 16;
    localparam PAGE_SWITCH_FIFO_AWIDTH = $clog2(PAGE_SWITCH_FIFO_DEPTH);

    localparam SWITCH_PAGE_INIT =0;
    localparam SWITCH_PAGE_GET =1;
    localparam SWITCH_PAGE_WAIT =2;
    localparam SWITCH_PAGE_CLEAN=3;
    localparam SWITCH_PAGE_DONE =4;  // Add new state for handshaking

    bit [2:0] current_switch_page_state;
    bit [2:0] next_switch_page_state;

    bit [63:0] page_id_fifo [0: PAGE_SWITCH_FIFO_DEPTH-1];
    bit [31:0] page_number_fifo [0: PAGE_SWITCH_FIFO_DEPTH-1];
    bit [PAGE_SWITCH_FIFO_AWIDTH:0] page_switch_fifo_rptr;
    bit [PAGE_SWITCH_FIFO_AWIDTH:0] page_switch_fifo_wptr;
    bit [63:0] page_req_id;
    bit [31:0] page_req_number;

    bit [PAGE_SWITCH_FIFO_AWIDTH:0] page_switch_fifo_left_space;
    bit page_switch_fifo_empty;
    bit is_page_req_fetched=0;  

    function automatic [PAGE_SWITCH_FIFO_AWIDTH:0] calculate_page_switch_fifo_space(
        input [PAGE_SWITCH_FIFO_AWIDTH:0] rptr,
        input [PAGE_SWITCH_FIFO_AWIDTH:0] wptr
    );
        if (rptr[PAGE_SWITCH_FIFO_AWIDTH] == ~wptr[PAGE_SWITCH_FIFO_AWIDTH]) begin
            calculate_page_switch_fifo_space = rptr[PAGE_SWITCH_FIFO_AWIDTH-1:0] - wptr[PAGE_SWITCH_FIFO_AWIDTH-1:0];
        end else begin
            calculate_page_switch_fifo_space = PAGE_SWITCH_FIFO_DEPTH - wptr[PAGE_SWITCH_FIFO_AWIDTH-1:0] + rptr[PAGE_SWITCH_FIFO_AWIDTH-1:0];
        end
    endfunction

    function automatic void h2s_switch_page_request(input longint page_id, input int unsigned page_number);
        if (page_switch_fifo_left_space > 0) begin
            page_id_fifo[page_switch_fifo_wptr[PAGE_SWITCH_FIFO_AWIDTH-1:0]] = page_id;
            page_number_fifo[page_switch_fifo_wptr[PAGE_SWITCH_FIFO_AWIDTH-1:0]] = page_number;
            page_switch_fifo_wptr = page_switch_fifo_wptr + 1;
        end
    endfunction

    function automatic bit is_rtl_read_clean(input bit [63:0] page_id, input bit [31:0] page_number);
        if (read_pipe_valid[0] && (read_pipe_offset_addr[0][63:PAGE_SIZE_AWIDTH] >= page_id) && (read_pipe_offset_addr[0][63:PAGE_SIZE_AWIDTH] < page_id + page_number)) begin
            return 0;
        end
        if (read_pipe_valid[1] && (read_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH] >= page_id) && (read_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH] < page_id + page_number)) begin
            return 0;
        end
        return 1;
    endfunction

    function automatic bit is_rtl_write_clean(input bit [63:0] page_id, input bit [31:0] page_number);
        if (write_pipe_valid[0] && (write_pipe_offset_addr[0][63:PAGE_SIZE_AWIDTH] >= page_id) && (write_pipe_offset_addr[0][63:PAGE_SIZE_AWIDTH] < page_id + page_number)) begin
            return 0;
        end
        if (write_pipe_valid[1] && (write_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH] >= page_id) && (write_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH] < page_id + page_number)) begin
            return 0;
        end
        return 1;
    endfunction

    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            current_switch_page_state <= SWITCH_PAGE_INIT;
        end else begin
            current_switch_page_state <= next_switch_page_state;
        end
    end

    always @(current_switch_page_state or page_switch_fifo_empty or read_pipe_valid[1] or read_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH] or write_pipe_valid[1] or write_pipe_offset_addr[1][63:PAGE_SIZE_AWIDTH] or dpi_wfifo_empty or dpi_rfifo_empty or dpi_read_valid_pipe[0] or dpi_write_valid_pipe[0] or page_req_id or page_req_number) begin
        case(current_switch_page_state)
            SWITCH_PAGE_INIT:
                if (page_switch_fifo_rptr != page_switch_fifo_wptr) begin
                    next_switch_page_state = SWITCH_PAGE_GET;
                end else begin
                    next_switch_page_state = SWITCH_PAGE_INIT;
                end
            SWITCH_PAGE_GET:
                next_switch_page_state = SWITCH_PAGE_WAIT;
            SWITCH_PAGE_WAIT:
                if (is_rtl_read_clean(page_req_id, page_req_number) && is_rtl_write_clean(page_req_id, page_req_number) && dpi_wfifo_empty && dpi_rfifo_empty && 
                    !dpi_read_valid_pipe[0] && !dpi_write_valid_pipe[0]) begin
                    next_switch_page_state = SWITCH_PAGE_CLEAN;
                end else begin
                    next_switch_page_state = SWITCH_PAGE_WAIT;
                end
            SWITCH_PAGE_CLEAN:
                if (page_switch_fifo_rptr != page_switch_fifo_wptr) begin
                    next_switch_page_state = SWITCH_PAGE_WAIT;
                end else begin
                    next_switch_page_state = SWITCH_PAGE_INIT;
                end
            default:
                next_switch_page_state = SWITCH_PAGE_INIT;
        endcase
    end

    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            page_switch_fifo_empty <= 1;
            page_switch_fifo_left_space <= PAGE_SWITCH_FIFO_DEPTH;
        end else begin
            page_switch_fifo_empty <= (page_switch_fifo_rptr == page_switch_fifo_wptr);
            page_switch_fifo_left_space <= calculate_page_switch_fifo_space(page_switch_fifo_rptr, page_switch_fifo_wptr);
        end
    end

    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            page_switch_fifo_rptr <= page_switch_fifo_wptr;
            is_page_req_fetched <= 0;
        end else begin
            case(current_switch_page_state)
                SWITCH_PAGE_INIT:
                begin
                    is_page_req_fetched <= 0;
                end
                SWITCH_PAGE_GET:
                begin
                    if (!is_page_req_fetched) begin
                        is_page_req_fetched <= 1;
                        page_req_id <= page_id_fifo[page_switch_fifo_rptr[PAGE_SWITCH_FIFO_AWIDTH-1:0]];
                        page_req_number <= page_number_fifo[page_switch_fifo_rptr[PAGE_SWITCH_FIFO_AWIDTH-1:0]];
                        page_switch_fifo_rptr <= page_switch_fifo_rptr + 1;
                    end 
                end
                SWITCH_PAGE_WAIT:
                begin
                end
                SWITCH_PAGE_CLEAN:
                begin
                    h2s_switch_page_response();
                    is_page_req_fetched <= 0;
                end
            endcase
        end
    end


    // Initialize BFM
    initial begin
        init_bfm(INST_ID, DATA_WIDTH, ADDR_WIDTH, PAGE_SIZE, CONT_LINE_PER_CHANNEL, MERGED_CHANNEL_NUM);
        rtl_notify_interval = 10;  // Default interval
        dpi_rfifo_wptr = 0;
        dpi_rfifo_rptr = 0;
        dpi_rfifo_empty = 1;
        dpi_wfifo_wptr = 0;
        dpi_wfifo_rptr = 0;
        dpi_wfifo_empty = 1;
`ifdef VP_TEST
        read_polling_recv_count = 0;
        write_polling_recv_count = 0;
        read_polling_send_count = 0;
        write_polling_send_count = 0;
        read_polling_count = 0;
        write_polling_count = 0;
        read_polling_interval = 100;
        write_polling_interval = 100;
`endif
        page_switch_fifo_wptr = 0;
        page_switch_fifo_rptr = 0;
        page_switch_fifo_empty = 1;
        page_switch_fifo_left_space = PAGE_SWITCH_FIFO_DEPTH;
        wdone = 1'b0;
    end


endmodule 
