`timescale 1ns/1ps

/**
 * @brief Generic Software FIFO to Hardware FIFO (SFIFO-HFIFO) Hardware Module
 *
 * This module provides a reusable abstraction for the hardware side of SFIFO-HFIFO mechanism:
 * - Hardware side maintains FIFOs for requests from software
 * - Periodically polls software side for new requests
 * - Supports batch operations for better performance
 * - Configurable FIFO depths and polling intervals
 */
module sfifo_hfifo_hw #(
    parameter FIFO_DEPTH = 16,
    parameter ADDR_WIDTH = 64,
    parameter DATA_WIDTH = 256,
    parameter DATA_WIDTH_IN_BYTE = DATA_WIDTH/8,
    parameter POLLING_INTERVAL = 100,
    parameter MAX_BATCH_SIZE = 4,
    parameter FIFO_TYPE = "WRITE",  // "WRITE" or "READ"
    parameter FIFO_AWIDTH=$clog2(FIFO_DEPTH)
)(
    input  wire clk,
    input  wire rstn,

    // FIFO status outputs
    output wire fifo_empty,
    output wire fifo_full,
    output wire [FIFO_AWIDTH:0] fifo_count,
    output wire [FIFO_AWIDTH:0] fifo_space,

    // Request output interface (to processing pipeline)
    output wire req_valid,
    output wire [ADDR_WIDTH-1:0] req_addr,
    output wire [DATA_WIDTH-1:0] req_data,     // Only for write requests
    output wire [DATA_WIDTH_IN_BYTE-1:0] req_mask, // Only for write requests
    input  wire req_ready,

    // DPI-C polling interface
    output wire polling_trigger
);


    // FIFO storage
    reg [ADDR_WIDTH-1:0] addr_fifo [0:FIFO_DEPTH-1];
    reg [DATA_WIDTH-1:0] data_fifo [0:FIFO_DEPTH-1];  // Only used for write type
    reg [DATA_WIDTH_IN_BYTE-1:0] mask_fifo [0:FIFO_DEPTH-1];  // Only used for write type

    // FIFO pointers
    reg [FIFO_AWIDTH:0] fifo_wptr;
    reg [FIFO_AWIDTH:0] fifo_rptr;

    // Polling control
    reg [31:0] polling_counter;
    reg [31:0] polling_interval;

    // Status signals
    wire fifo_empty_int;
    wire fifo_full_int;
    wire [FIFO_AWIDTH:0] fifo_count_int;
    wire [FIFO_AWIDTH:0] fifo_space_int;

    // FIFO status calculation
    assign fifo_empty_int = (fifo_rptr == fifo_wptr);
    assign fifo_full_int = (fifo_wptr[FIFO_AWIDTH] != fifo_rptr[FIFO_AWIDTH]) &&
                          (fifo_wptr[FIFO_AWIDTH-1:0] == fifo_rptr[FIFO_AWIDTH-1:0]);

    function automatic [FIFO_AWIDTH:0] calculate_fifo_count(
        input [FIFO_AWIDTH:0] wptr,
        input [FIFO_AWIDTH:0] rptr
    );
        if (wptr[FIFO_AWIDTH] == rptr[FIFO_AWIDTH]) begin
            calculate_fifo_count = wptr[FIFO_AWIDTH-1:0] - rptr[FIFO_AWIDTH-1:0];
        end else begin
            calculate_fifo_count = FIFO_DEPTH - rptr[FIFO_AWIDTH-1:0] + wptr[FIFO_AWIDTH-1:0];
        end
    endfunction

    function automatic [FIFO_AWIDTH:0] calculate_fifo_space(
        input [FIFO_AWIDTH:0] wptr,
        input [FIFO_AWIDTH:0] rptr
    );
        calculate_fifo_space = FIFO_DEPTH - calculate_fifo_count(wptr, rptr);
    endfunction

    assign fifo_count_int = calculate_fifo_count(fifo_wptr, fifo_rptr);
    assign fifo_space_int = calculate_fifo_space(fifo_wptr, fifo_rptr);

    // Output assignments
    assign fifo_empty = fifo_empty_int;
    assign fifo_full = fifo_full_int;
    assign fifo_count = fifo_count_int;
    assign fifo_space = fifo_space_int;

    // Request output
    assign req_valid = !fifo_empty_int;
    assign req_addr = addr_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];

    generate
        if (FIFO_TYPE == "WRITE") begin : gen_write_outputs
            assign req_data = data_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
            assign req_mask = mask_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
        end else begin : gen_read_outputs
            assign req_data = {DATA_WIDTH{1'b0}};
            assign req_mask = {DATA_WIDTH_IN_BYTE{1'b0}};
        end
    endgenerate

    // Polling trigger generation
    assign polling_trigger = (polling_counter == polling_interval) &&
                            (fifo_space_int >= MAX_BATCH_SIZE);

    // Polling counter
    always @(posedge clk) begin
        if (!rstn) begin
            polling_counter <= 0;
            polling_interval <= POLLING_INTERVAL;
        end else begin
            if (polling_counter < polling_interval) begin
                polling_counter <= polling_counter + 1;
            end else begin
                polling_counter <= 0;
            end
        end
    end

    // FIFO read logic
    always @(posedge clk) begin
        if (!rstn) begin
            fifo_rptr <= 0;
        end else begin
            if (req_valid && req_ready) begin
                fifo_rptr <= fifo_rptr + 1;
            end
        end
    end

    // FIFO write interface functions (to be called by DPI-C)
    function automatic int push_write_request(
        input bit [ADDR_WIDTH-1:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask
    );
        if (!fifo_full_int) begin
            addr_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = addr;
            if (FIFO_TYPE == "WRITE") begin
                data_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = data;
                mask_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = mask;
            end
            fifo_wptr = fifo_wptr + 1;
            return 1;
        end else begin
            return 0;
        end
    endfunction

    function automatic int push_read_request(
        input bit [ADDR_WIDTH-1:0] addr
    );
        if (!fifo_full_int) begin
            addr_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = addr;
            fifo_wptr = fifo_wptr + 1;
            return 1;
        end else begin
            return 0;
        end
    endfunction

    // Batch write function for better performance
    function automatic int push_write_batch(
        input bit [ADDR_WIDTH-1:0] addr_array [0:MAX_BATCH_SIZE-1],
        input bit [DATA_WIDTH-1:0] data_array [0:MAX_BATCH_SIZE-1],
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask_array [0:MAX_BATCH_SIZE-1],
        input int batch_size
    );
        int pushed_count = 0;
        for (int i = 0; i < batch_size && !fifo_full_int; i++) begin
            if (push_write_request(addr_array[i], data_array[i], mask_array[i])) begin
                pushed_count++;
            end else begin
                break;
            end
        end
        return pushed_count;
    endfunction

    // Batch read function for better performance
    function automatic int push_read_batch(
        input bit [ADDR_WIDTH-1:0] addr_array [0:MAX_BATCH_SIZE-1],
        input int batch_size
    );
        int pushed_count = 0;
        for (int i = 0; i < batch_size && !fifo_full_int; i++) begin
            if (push_read_request(addr_array[i])) begin
                pushed_count++;
            end else begin
                break;
            end
        end
        return pushed_count;
    endfunction

    // Initialize
    initial begin
        fifo_wptr = 0;
        fifo_rptr = 0;
        polling_counter = 0;
        polling_interval = POLLING_INTERVAL;
    end

endmodule

/**
 * @brief Integrated SFIFO-HFIFO module with DPI-C interface
 *
 * This module combines the generic SFIFO-HFIFO hardware with DPI-C interfaces
 * to provide a complete solution that can be easily integrated into existing designs.
 */
module sfifo_hfifo_integrated #(
    parameter INSTANCE_ID = 0,
    parameter FIFO_DEPTH = 16,
    parameter ADDR_WIDTH = 64,
    parameter DATA_WIDTH = 256,
    parameter DATA_WIDTH_IN_BYTE = DATA_WIDTH/8,
    parameter POLLING_INTERVAL = 100,
    parameter MAX_BATCH_SIZE = 4
)(
    input  wire clk,
    input  wire rstn,

    // Write request output interface
    output wire write_req_valid,
    output wire [ADDR_WIDTH-1:0] write_req_addr,
    output wire [DATA_WIDTH-1:0] write_req_data,
    output wire [DATA_WIDTH_IN_BYTE-1:0] write_req_mask,
    input  wire write_req_ready,

    // Read request output interface
    output wire read_req_valid,
    output wire [ADDR_WIDTH-1:0] read_req_addr,
    input  wire read_req_ready,

    // Status outputs
    output wire write_fifo_empty,
    output wire write_fifo_full,
    output wire read_fifo_empty,
    output wire read_fifo_full
);

    localparam FIFO_DEPTH_AWIDTH = $clog2(FIFO_DEPTH);

    // DPI-C function imports
    import "DPI-C" context function void sfifo_hfifo_init(
        input shortint unsigned instance_id,
        input int max_batch_size
    );

    import "DPI-C" context function int sfifo_hfifo_poll_write(
        input shortint unsigned instance_id,
        input int hw_available_space
    );

    import "DPI-C" context function int sfifo_hfifo_poll_read(
        input shortint unsigned instance_id,
        input int hw_available_space
    );

    // Export functions for software to push requests
    export "DPI-C" function hw_push_write_request;
    export "DPI-C" function hw_push_read_request;
    export "DPI-C" function hw_push_write_batch;
    export "DPI-C" function hw_push_read_batch;

    // Write FIFO instance
    wire write_polling_trigger;
    wire [FIFO_DEPTH_AWIDTH:0] write_fifo_space;

    sfifo_hfifo_hw #(
        .FIFO_DEPTH(FIFO_DEPTH),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .DATA_WIDTH_IN_BYTE(DATA_WIDTH_IN_BYTE),
        .POLLING_INTERVAL(POLLING_INTERVAL),
        .MAX_BATCH_SIZE(MAX_BATCH_SIZE),
        .FIFO_TYPE("WRITE")
    ) write_fifo_inst (
        .clk(clk),
        .rstn(rstn),
        .fifo_empty(write_fifo_empty),
        .fifo_full(write_fifo_full),
        .fifo_space(write_fifo_space),
        .req_valid(write_req_valid),
        .req_addr(write_req_addr),
        .req_data(write_req_data),
        .req_mask(write_req_mask),
        .req_ready(write_req_ready),
        .polling_trigger(write_polling_trigger)
    );

    // Read FIFO instance
    wire read_polling_trigger;
    wire [FIFO_DEPTH_AWIDTH:0] read_fifo_space;

    sfifo_hfifo_hw #(
        .FIFO_DEPTH(FIFO_DEPTH),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .DATA_WIDTH_IN_BYTE(DATA_WIDTH_IN_BYTE),
        .POLLING_INTERVAL(POLLING_INTERVAL),
        .MAX_BATCH_SIZE(MAX_BATCH_SIZE),
        .FIFO_TYPE("READ")
    ) read_fifo_inst (
        .clk(clk),
        .rstn(rstn),
        .fifo_empty(read_fifo_empty),
        .fifo_full(read_fifo_full),
        .fifo_space(read_fifo_space),
        .req_valid(read_req_valid),
        .req_addr(read_req_addr),
        .req_data(),  // Not used for read
        .req_mask(),  // Not used for read
        .req_ready(read_req_ready),
        .polling_trigger(read_polling_trigger)
    );


    // Polling logic
    always @(posedge clk) begin
        if (rstn) begin
            if (write_polling_trigger) begin
                sfifo_hfifo_poll_write(INSTANCE_ID, write_fifo_space);
            end

            if (read_polling_trigger) begin
                sfifo_hfifo_poll_read(INSTANCE_ID, read_fifo_space);
            end
        end
    end

    // DPI-C functions for software to push requests
    function automatic int hw_push_write_request(
        input bit [ADDR_WIDTH-1:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask,
        input int data_words
    );
        return write_fifo_inst.push_write_request(addr, data, mask);
    endfunction

    function automatic int hw_push_read_request(
        input bit [ADDR_WIDTH-1:0] addr
    );
        return read_fifo_inst.push_read_request(addr);
    endfunction

    function automatic int hw_push_write_batch(
        input bit [ADDR_WIDTH-1:0] addr_array [0:MAX_BATCH_SIZE-1],
        input bit [DATA_WIDTH-1:0] data_array [0:MAX_BATCH_SIZE-1],
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask_array [0:MAX_BATCH_SIZE-1],
        input int batch_size,
        input int data_words_per_req
    );
        return write_fifo_inst.push_write_batch(addr_array, data_array, mask_array, batch_size);
    endfunction

    function automatic int hw_push_read_batch(
        input bit [ADDR_WIDTH-1:0] addr_array [0:MAX_BATCH_SIZE-1],
        input int batch_size
    );
        return read_fifo_inst.push_read_batch(addr_array, batch_size);
    endfunction

    // Initialize
    initial begin
        sfifo_hfifo_init(INSTANCE_ID, MAX_BATCH_SIZE);
    end

endmodule
