module memory_counter_optimized #(parameter ADDR_WIDTH=21, DATA_WIDTH=64) (
    input clk,
    input rst_n,
    input ren,
    input [ADDR_WIDTH-1:0] addr,
    output reg [DATA_WIDTH-1:0] data_out,
    output reg valid
);
    
    // Memory definition
    reg [DATA_WIDTH-1:0] mem [0:(1<<ADDR_WIDTH)-1];
    // Page access counter
    reg [63:0] page_read_stat [0:(1<<ADDR_WIDTH)-1];
    // Valid table
    reg valid_table [0:(1<<ADDR_WIDTH)-1];
    
    // FSM states
    typedef enum logic [2:0] {
        IDLE      = 3'b000,
        ADDR_CMP  = 3'b001,  // Stage 1: Address comparison
        MEM_READ  = 3'b010,  // Stage 2: Memory read
        CNT_UPD   = 3'b100   // Stage 3: Counter update
    } state_t;
    
    state_t current_state, next_state;
    
    // Pipeline registers
    reg [ADDR_WIDTH-1:0] pipe_addr [0:2];  // 3 stages
    reg [DATA_WIDTH-1:0] pipe_data;
    reg [63:0] pipe_count;
    reg valid_bit;
    reg addr_is_same;
    
    // Address tracking
    reg [ADDR_WIDTH-1:0] current_addr;
    
    // Local count for consecutive accesses
    reg [63:0] local_count;
    
    // FSM state transition
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            current_state <= IDLE;
        end else begin
            current_state <= next_state;
        end
    end
    
    // Next state logic
    always @(*) begin
        case (current_state)
            IDLE: begin
                if (ren) next_state = ADDR_CMP;
                else next_state = IDLE;
            end
            ADDR_CMP: next_state = MEM_READ;
            MEM_READ: next_state = CNT_UPD;
            CNT_UPD: begin
                if (ren) next_state = ADDR_CMP;
                else next_state = IDLE;
            end
            default: next_state = IDLE;
        endcase
    end
    
    // Pipeline control signals
    reg pipe_valid [0:2];  // Valid signal for each stage
    
    // Stage 1: Address comparison and input capture
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pipe_addr[0] <= {ADDR_WIDTH{1'b0}};
            current_addr <= {ADDR_WIDTH{1'b0}};
            addr_is_same <= 1'b0;
            valid_bit <= 1'b0;
            pipe_valid[0] <= 1'b0;
        end else begin
            case (current_state)
                IDLE: begin
                    if (ren) begin
                        pipe_addr[0] <= addr;
                        valid_bit <= valid_table[addr];
                        addr_is_same <= (addr == current_addr);
                        if (addr != current_addr) begin
                            current_addr <= addr;
                        end
                        pipe_valid[0] <= 1'b1;
                    end else begin
                        pipe_valid[0] <= 1'b0;
                    end
                end
                
                CNT_UPD: begin
                    if (ren) begin
                        pipe_addr[0] <= addr;
                        valid_bit <= valid_table[addr];
                        addr_is_same <= (addr == current_addr);
                        if (addr != current_addr) begin
                            current_addr <= addr;
                        end
                        pipe_valid[0] <= 1'b1;
                    end else begin
                        pipe_valid[0] <= 1'b0;
                    end
                end
                
                default: begin
                    pipe_valid[0] <= 1'b0;
                end
            endcase
        end
    end
    
    // Stage 2: Memory read and counter preparation
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pipe_addr[1] <= {ADDR_WIDTH{1'b0}};
            pipe_data <= {DATA_WIDTH{1'b0}};
            pipe_count <= 64'd0;
            local_count <= 64'd0;
            pipe_valid[1] <= 1'b0;
        end else begin
            case (current_state)
                ADDR_CMP: begin
                    pipe_addr[1] <= pipe_addr[0];
                    if (valid_bit) begin
                        pipe_data <= mem[pipe_addr[0]];
                    end else begin
                        pipe_data <= {DATA_WIDTH{1'b0}};
                    end
                    
                    if (addr_is_same) begin
                        pipe_count <= local_count;
                        local_count <= local_count + 1;
                    end else begin
                        pipe_count <= page_read_stat[pipe_addr[0]];
                        local_count <= page_read_stat[pipe_addr[0]] + 1;
                    end
                    pipe_valid[1] <= pipe_valid[0];
                end
                
                default: begin
                    pipe_valid[1] <= 1'b0;
                end
            endcase
        end
    end
    
    // Stage 3: Output and counter update
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pipe_addr[2] <= {ADDR_WIDTH{1'b0}};
            data_out <= {DATA_WIDTH{1'b0}};
            valid <= 1'b0;
            pipe_valid[2] <= 1'b0;
        end else begin
            case (current_state)
                MEM_READ: begin
                    pipe_addr[2] <= pipe_addr[1];
                    data_out <= pipe_data;
                    valid <= pipe_valid[1];
                    if (pipe_valid[1]) begin
                        page_read_stat[pipe_addr[1]] <= pipe_count + 1;
                    end
                    pipe_valid[2] <= pipe_valid[1];
                end
                
                default: begin
                    valid <= 1'b0;
                    pipe_valid[2] <= 1'b0;
                end
            endcase
        end
    end
    
endmodule
