`timescale 1ns/1ps

module dut (input clk, input rstn, input ren, input [9:0] raddr, output reg [63:0] rdata);

    reg [63:0] mem [0:1023];
    reg ren_d1;
    reg [9:0] raddr_d1;

    always_ff @(posedge clk or negedge rstn) begin
        if (!rstn) begin
            rdata <= 0;
            ren_d1 <= 0;
            raddr_d1 <= 0;
        end else begin
            if (ren_d1) begin
                rdata <= mem[raddr_d1];
            end
            ren_d1 <= ren;
            raddr_d1 <= raddr;
        end
    end


endmodule


module test;

    reg clk=0;
    reg rstn=0;
    reg ren;
    reg [9:0] raddr;
    wire [63:0] rdata;

    dut u_dut (.*);

    always #5 clk = ~clk;

    initial begin
        u_dut.mem[0] = 64'h1234567890abcdef;
        u_dut.mem[1] = 64'h0987654321abcdef;
        u_dut.mem[2] = 64'h1234567890abcdef;
        u_dut.mem[3] = 64'h0987654321abcdef;
        u_dut.mem[4] = 64'h1234567890abcdef;
        u_dut.mem[5] = 64'h0987654321abcdef;
        u_dut.mem[6] = 64'h1234567890abcdef;
        u_dut.mem[7] = 64'h0987654321abcdef;
        u_dut.mem[8] = 64'h1234567890abcdef;
        u_dut.mem[9] = 64'h0987654321abcdef;
        u_dut.mem[10] = 64'h1234567890abcdef;
        u_dut.mem[11] = 64'h0987654321abcdef;
        rstn <= 0;
        ren <= 0;
        repeat(10) @(posedge clk);
        rstn <= 1;
        ren <= 1;
        raddr <= 10'h0;
        @(posedge clk);
        @(posedge clk);
        raddr <= 10'h1;
        @(posedge clk);
        ren <= 0;
        @(posedge clk);
        ren <= 1;
        raddr <= 10'h2;
        @(posedge clk);
        ren <= 0;
        repeat(10) @(posedge clk);
        raddr <= 10'h3;
        repeat(10)@(posedge clk);
        $finish;
    end

endmodule



