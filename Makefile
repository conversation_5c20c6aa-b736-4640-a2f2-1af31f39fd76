# Makefile for SFIFO-HFIFO abstraction modules

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -pthread
INCLUDES = -I. -Iverif/vpWrHwPageSync/src/cc -I${VCS_HOME}/include

# Source files
WRAPPER_SOURCES = verif/vpWrHwPageSync/src/cc/sfifo_hfifo_wrapper.cpp
TEST_SOURCES = test/test_sfifo_hfifo.cpp
EXAMPLE_SOURCES = examples/sfifo_hfifo_usage_example.cpp

# Header files
HEADERS = verif/vpWrHwPageSync/src/cc/sfifo_hfifo_client.h \
          verif/vpWrHwPageSync/src/cc/sfifo_hfifo_wrapper.h

# Output directories
BUILD_DIR = build
TEST_DIR = $(BUILD_DIR)/test
EXAMPLE_DIR = $(BUILD_DIR)/examples

# Targets
.PHONY: all clean test examples help

all: test examples

# Create build directories
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(TEST_DIR): $(BUILD_DIR)
	mkdir -p $(TEST_DIR)

$(EXAMPLE_DIR): $(BUILD_DIR)
	mkdir -p $(EXAMPLE_DIR)

# Test target
test: $(TEST_DIR)/test_sfifo_hfifo
	@echo "Running SFIFO-HFIFO tests..."
	@$(TEST_DIR)/test_sfifo_hfifo

$(TEST_DIR)/test_sfifo_hfifo: $(TEST_SOURCES) $(HEADERS) | $(TEST_DIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $(TEST_SOURCES)

# Examples target
examples: $(EXAMPLE_DIR)/usage_example

$(EXAMPLE_DIR)/usage_example: $(EXAMPLE_SOURCES) $(WRAPPER_SOURCES) $(HEADERS) | $(EXAMPLE_DIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -shared -fPIC -o $@.so $(EXAMPLE_SOURCES) $(WRAPPER_SOURCES)

# Clean target
clean:
	rm -rf $(BUILD_DIR)

# Help target
help:
	@echo "SFIFO-HFIFO Abstraction Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  all      - Build tests and examples"
	@echo "  test     - Build and run unit tests"
	@echo "  examples - Build usage examples"
	@echo "  clean    - Remove build artifacts"
	@echo "  help     - Show this help message"
	@echo ""
	@echo "Files:"
	@echo "  Headers:"
	@echo "    verif/vpWrHwPageSync/src/cc/sfifo_hfifo_client.h    - Template client class"
	@echo "    verif/vpWrHwPageSync/src/cc/sfifo_hfifo_wrapper.h   - Wrapper interface"
	@echo "  Sources:"
	@echo "    verif/vpWrHwPageSync/src/cc/sfifo_hfifo_wrapper.cpp - Wrapper implementation"
	@echo "  SystemVerilog:"
	@echo "    rtl/sfifo_hfifo_hw.sv                               - Hardware modules"
	@echo "  Tests:"
	@echo "    test/test_sfifo_hfifo.cpp                           - Unit tests"
	@echo "  Examples:"
	@echo "    examples/sfifo_hfifo_usage_example.cpp              - C++ usage example"
	@echo "    examples/sfifo_hfifo_usage_example.sv               - SystemVerilog example"

# Check syntax
check-syntax:
	@echo "Checking C++ syntax..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only $(WRAPPER_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only $(TEST_SOURCES)
	@echo "C++ syntax check passed!"

# Documentation target
docs:
	@echo "SFIFO-HFIFO Abstraction Documentation"
	@echo "======================================"
	@echo ""
	@echo "This project abstracts the SFIFO-HFIFO mechanism from hbm3_pc_bfm.sv"
	@echo "into reusable, optimized modules."
	@echo ""
	@echo "Key Features:"
	@echo "  • Batch processing optimization (1 -> N requests per transfer)"
	@echo "  • Template-based type safety"
	@echo "  • Configurable parameters"
	@echo "  • Clean separation of software/hardware concerns"
	@echo "  • Thread-safe operations"
	@echo ""
	@echo "Usage:"
	@echo "  1. Include sfifo_hfifo_client.h in your C++ code"
	@echo "  2. Instantiate sfifo_hfifo_integrated in your SystemVerilog"
	@echo "  3. Use SfifoHfifoWrapper for high-level management"
	@echo ""
	@echo "See README_SFIFO_HFIFO.md for detailed documentation."

# Performance test (if we had performance benchmarks)
perf-test:
	@echo "Performance comparison:"
	@echo "  Original: 1 request per polling cycle"
	@echo "  Optimized: Up to MAX_BATCH_SIZE requests per polling cycle"
	@echo "  Theoretical speedup: $(MAX_BATCH_SIZE)x (with MAX_BATCH_SIZE=4)"
	@echo ""
	@echo "Run 'make test' to verify functionality."

# Show file structure
structure:
	@echo "Project Structure:"
	@echo "├── verif/vpWrHwPageSync/src/cc/"
	@echo "│   ├── sfifo_hfifo_client.h          # Template client classes"
	@echo "│   ├── sfifo_hfifo_wrapper.h         # Wrapper interface"
	@echo "│   └── sfifo_hfifo_wrapper.cpp       # Wrapper implementation"
	@echo "├── rtl/"
	@echo "│   └── sfifo_hfifo_hw.sv             # Hardware modules"
	@echo "├── examples/"
	@echo "│   ├── sfifo_hfifo_usage_example.cpp # C++ usage example"
	@echo "│   └── sfifo_hfifo_usage_example.sv  # SystemVerilog example"
	@echo "├── test/"
	@echo "│   └── test_sfifo_hfifo.cpp          # Unit tests"
	@echo "├── README_SFIFO_HFIFO.md             # Documentation"
	@echo "└── Makefile                          # This file"
