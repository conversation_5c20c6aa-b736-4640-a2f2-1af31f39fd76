`timescale 1ns/1ps

`define DEBUG 1

module hbm3_pc_bfm_tb;

    // Parameters
    localparam ADDR_WIDTH = 10;
    localparam DATA_WIDTH = 256;
    localparam DATA_BYTE_WIDTH = DATA_WIDTH/8;
    localparam PAGE_SIZE = 256*16;
    localparam [9:0] HBM_ID = 1;
    localparam [4:0] CHANNEL_ID = 7;
    localparam [0:0] PC_ID = 1;

    localparam BFM_NUM = 2;

    // Clock and reset
    reg clk;
    reg rstn;

    // Write interface signals
    reg                         mm_wen [0: BFM_NUM-1];
    reg [64-1:0]               mm_waddr [0:BFM_NUM-1];
    reg [DATA_WIDTH-1:0]       mm_wdata [0:BFM_NUM-1];
    reg [DATA_BYTE_WIDTH-1:0]  mm_wmask [0:BFM_NUM-1];

    // Read interface signals
    reg                         mm_ren [0:BFM_NUM-1];
    reg [64-1:0]               mm_raddr [0:BFM_NUM-1];
    wire [DATA_WIDTH-1:0]      mm_rdata [0:BFM_NUM-1];
    wire                       mm_rvalid [0:BFM_NUM-1];

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;
    end

    // DUT instantiation
    genvar inst_num;
    generate
        for (inst_num = 0; inst_num < BFM_NUM; inst_num = inst_num + 1) begin :dut
            hbm3_pc_bfm #(
                .HBM_ID(HBM_ID),
                .CHANNEL_ID(CHANNEL_ID+inst_num/2),
                .PC_ID(PC_ID+inst_num%2),
                .ADDR_WIDTH(ADDR_WIDTH),
                .DATA_WIDTH(DATA_WIDTH),
                .BASE_ADDR(64'h80000000),
                .PAGE_SIZE(PAGE_SIZE),
                .CHANNEL_NUM(8)
            ) u_hbm3_pc_bfm (
                .mm_clk(clk),
                .mm_rstn(rstn),
                // Write port
                .mm_wen(mm_wen[inst_num]),
                .mm_waddr(mm_waddr[inst_num]),
                .mm_wdata(mm_wdata[inst_num]),
                .mm_wmask(mm_wmask[inst_num]),
                // Read port
                .mm_ren(mm_ren[inst_num]),
                .mm_raddr(mm_raddr[inst_num]),
                .mm_rdata(mm_rdata[inst_num]),
                .mm_rvalid(mm_rvalid[inst_num])
            );
        end
    endgenerate

    bit [15:0] id = {PC_ID, CHANNEL_ID, HBM_ID};

    bit [DATA_WIDTH-1:0] wdata [0:BFM_NUM-1];
    bit [DATA_WIDTH-1:0] rdata [0:BFM_NUM-1];
    bit [DATA_BYTE_WIDTH-1:0] wmask={DATA_BYTE_WIDTH{1'b1}};
    bit [63:0] addr [0:BFM_NUM-1];

    event initmemdone;
    event resetev;

    generate
        for(genvar i = 0; i < BFM_NUM; i++) begin : initmem
            initial begin
                $readmemh("mem.hex", dut[i].u_hbm3_pc_bfm.memory);
                $readmemh("page.hex", dut[i].u_hbm3_pc_bfm.page_table);
                $display("[SV] Memory and page table initialized %d", i);
                -> initmemdone;
            end
        end
    endgenerate

    initial begin
        @(initmemdone);

        //#100;

        for (int i = 0; i < BFM_NUM; i++) begin
            mm_wen[i] <= 0;
            mm_waddr[i] <= 0;
            mm_wdata[i] <= 0;
            mm_wmask[i] <= 0;
            mm_ren[i] <= 0;
            mm_raddr[i] <= 0;
        end

        $display("[SV] Reset sequence");

        // Reset sequence
        rstn <= 0;
        #100;
        rstn <= 1;

        #100;

        -> resetev;
    end

    // make sure the address is different for different PC instances. or the fakeC doens't work
    generate
        for(genvar i = 0; i < BFM_NUM; i++) begin : test
            initial begin
            @(resetev);

            fork 
                begin

                    //#(i*100);

                    // Test 1
                    wdata[i] <= 256'h0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0_a0a0_a0a0_a0a0_a0a0_a0a0 + i;
                    addr[i] <= 64'h80000120 + i*32*32;

                    @(posedge clk);

                    dut[i].u_hbm3_pc_bfm.start_write_test(addr[i], wdata[i], wmask);
                    #10;
                    dut[i].u_hbm3_pc_bfm.start_read_test(addr[i]);
                    repeat(10) @(posedge clk);
                    rdata[i] = dut[i].u_hbm3_pc_bfm.start_read_back(addr[i]);

                    if (rdata[i] === wdata[i]) begin
                        $display("[SV] inst %d Test 1 passed", i);
                    end else begin
                        $display("[SV] inst %d Test 1 **failed** rdata=%x wdata=%x", i, rdata[i], wdata[i]);
                    end


                    // Test 2

                    wdata[i] <= 256'h5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_a5a5 + i;
                    addr[i] <= 64'h80000120 + i*32*32;

                    @(posedge clk);

                    dut[i].u_hbm3_pc_bfm.start_write_test(addr[i], wdata[i], wmask);
                    #10;
                    dut[i].u_hbm3_pc_bfm.start_read_test(addr[i]);
                    repeat(10) @(posedge clk);
                    rdata[i] = dut[i].u_hbm3_pc_bfm.start_read_back(addr[i]);

                    if (rdata[i] === wdata[i]) begin
                        $display("[SV] inst %d Test 2 passed", i);
                    end else begin
                        $display("[SV] inst %d Test 2 **failed** rdata=%x wdata=%x", i, rdata[i], wdata[i]);
                    end
                    repeat(1000) @(posedge clk);
                end
                begin
                    repeat(10)@(posedge clk);
                    mm_wen[i] <= 1;
                    mm_waddr[i] <= 64'h80000140 + i*32*32;
                    mm_wdata[i] <= {DATA_WIDTH{1'b1}};  // All 1's
                    mm_wmask[i] <= {DATA_BYTE_WIDTH{1'b1}};  // All bytes enabled
                    
                    @(posedge clk);
                    mm_wen[i] <= 0;
                    mm_waddr[i] <= 64'h00000000;
                    
                    // Wait a few cycles
                    repeat(5) @(posedge clk);
                    
                    // Read from address 0
                    mm_ren[i] <= 1;
                    mm_raddr[i] <= 64'h80000140 + i*32*32;
                    
                    @(posedge clk);
                    mm_ren[i] <= 0;
                    mm_raddr[i] <= 64'h00000000;
                    
                    // Wait for read valid
                    wait(mm_rvalid[i]);
                    if(mm_rdata[i] === {DATA_WIDTH{1'b1}}) begin
                        $display("Test 1 PASSED: Read data matches written data");
                    end else begin
                        $display("Test 1 FAILED: Read data mismatch");
                    end

                    // Test 2: Write with byte enables
                    @(posedge clk);
                    mm_wen[i] <= 1;
                    mm_waddr[i] <= 64'h80000760 + i * 32 * 32;
                    mm_wdata[i] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5;
                    mm_wmask[i] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
                    mm_wmask[i][0] <= 1'b1;  // Enable only first byte
                    
                    @(posedge clk);
                    mm_wen[i] <= 0;
                    mm_waddr[i] <= 64'h00000000;
                    
                    repeat(5) @(posedge clk);
                    
                    mm_ren[i] <= 1;
                    mm_raddr[i] <= 64'h80000760 + i * 32 * 32;
                    
                    @(posedge clk);
                    mm_ren[i] <= 0;
                    mm_raddr[i] <= 64'h00000000;
                    
                    wait(mm_rvalid[i]);
                    if(mm_rdata[i][7:0] === 8'ha5) begin
                        $display("Test 2 PASSED: Byte enable working correctly");
                    end else begin
                        $display("Test 2 FAILED: Byte enable not working");
                    end

                    // Add more test cases here...
                    // Test 3: write write read read
                    @(posedge clk);
                    mm_wen[i] <= 1;
                    mm_waddr[i] <= 64'h80000120 + i*32*32;
                    mm_wdata[i] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5;
                    mm_wmask[i] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
                    
                    @(posedge clk);
                    mm_wen[i] <= 0;
                    mm_waddr[i] <= 64'h00000000;
                    
                    repeat(3) @(posedge clk);

                    mm_wen[i] <= 1;
                    mm_waddr[i] <= 64'h80000180 + i*32*32;
                    mm_wdata[i] <= 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a;
                    mm_wmask[i] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
                    
                    @(posedge clk);
                    mm_wen[i] <= 0;
                    mm_waddr[i] <= 64'h00000000;

                    dut[i].u_hbm3_pc_bfm.h2s_switch_page_request(8,1);
                    
                    repeat(3) @(posedge clk);
                    
                    mm_ren[i] <= 1;
                    mm_raddr[i] <= 64'h80000180 + i*32*32;
                    
                    @(posedge clk);
                    mm_ren[i] <= 0;
                    mm_raddr[i] <= 64'h00000000;
                    
                    wait(mm_rvalid[i]);
                    if(mm_rdata[i][DATA_WIDTH-1:0] === 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5) begin
                        $display("Test 3-1 PASSED: write/read working correctly");
                    end else begin
                        $display("Test 3-1 FAILED: write/read not working");
                    end

                    dut[i].u_hbm3_pc_bfm.h2s_switch_page_request(9,1);

                    repeat(2) @(posedge clk);
                    
                    mm_ren[i] <= 1;
                    mm_raddr[i] <= 64'h80000180 + i*32*32;
                    
                    @(posedge clk);
                    mm_ren[i] <= 0;
                    mm_raddr[i] <= 64'h00000000;
                    
                    wait(mm_rvalid[i]);
                    if(mm_rdata[i][DATA_WIDTH-1:0] === 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a) begin
                        $display("Test 3-2 PASSED: write/read working correctly");
                    end else begin
                        $display("Test 3-2 FAILED: write/read not working");
                    end

                    // Write to other page
                    for (int j=0;j<40;j=j+1) begin
                        dut[i].u_hbm3_pc_bfm.h2s_switch_page_request(j,1+j);
                        @(posedge clk);
                        mm_wen[i] <= 1;
                        mm_waddr[i] <= 64'h800002e0 + j*32*16*4;
                        mm_wdata[i] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a500 + j;
                        mm_wmask[i] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled

                        
                        @(posedge clk);
                        mm_wen[i] <= 0;
                        mm_waddr[i] <= 64'h00000000;
                        
                        repeat(5) @(posedge clk);
                        
                        mm_ren[i] <= 1;
                        mm_raddr[i] <= 64'h800002e0 + j*32*16*4;
                        
                        @(posedge clk);
                        mm_ren[i] <= 0;
                        mm_raddr[i] <= 64'h00000000;
                        
                        wait(mm_rvalid[i]);
                        @(posedge clk);
                        if(mm_rdata[i] === (256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a500 +j)) begin
                            $display("%d: Test 4-%d PASSED: working correctly", i, j);
                        end else begin
                            $display("%d: Test 4-%d FAILED: not working", i, j);
                        end
                    end
                end
            join

            repeat(100) @(posedge clk);
            $finish;
        end //initial
    end // for
    endgenerate



endmodule 
