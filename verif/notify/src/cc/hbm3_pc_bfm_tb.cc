#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <thread>
#include <chrono>
#include <assert.h>
#include <vector>
#include <unordered_map>
#include "svdpi.h"

// Constants matching the Verilog parameters
#define ADDR_WIDTH 10
#define DATA_WIDTH 256
#define DATA_BYTE_WIDTH (DATA_WIDTH/8)
#define PAGE_SIZE (64*8)
#define MAX_BFM_NUM 16  // Maximum number of BFM instances we support

// Forward declarations of exported functions from Verilog (s2h = SystemVerilog to Host)
extern "C" {
    // Functions exported from SystemVerilog
    extern void s2h_pc_write_req(svBitVecVal* addr, svBitVecVal* data, 
                                int data_size_in_bits, svBitVecVal* mask);
    extern void s2h_pc_read_req(svBitVecVal* addr, int data_size_in_bits);
    extern void s2h_pc_read_data(svBitVecVal* data, int data_size_in_bits);
}

// Structure to store BFM instance information
struct BFMInstance {
    uint16_t id;
    svScope scope;
    bool is_valid;
};

class BFMManager {
private:
    BFMManager() : thread_running_(false), num_instances_(0) {}
    ~BFMManager() { cleanup(); }

    // Prevent copying
    BFMManager(const BFMManager&) = delete;
    BFMManager& operator=(const BFMManager&) = delete;

    std::thread test_thread_;
    bool thread_running_;
    BFMInstance instances_[MAX_BFM_NUM];
    uint32_t num_instances_;

    void vp_test_function() {
        while (thread_running_) {
            for (uint32_t i = 0; i < num_instances_; i++) {
                if (!instances_[i].is_valid) continue;

                // Set scope for this instance
                //svScope old_scope = svSetScope(instances_[i].scope);
                //if (!old_scope) {
                //    printf("Error: Failed to set scope for BFM ID=%d\n", instances_[i].id);
                //    assert(0);
                //}

                // Prepare test data
                svBitVecVal test_data[DATA_WIDTH/32];
                char test_mask[DATA_BYTE_WIDTH];
                
                for (int j = 0; j < DATA_WIDTH/32; j++) {
                    test_data[j] = 0xAAAAAAAA;
                }
                memset(test_mask, 0xFF, DATA_BYTE_WIDTH);

                // Test VP write request
                uint64_t addr = 0x1000;
                s2h_pc_write_req(
                    reinterpret_cast<svBitVecVal*>(&addr),
                    test_data,
                    DATA_WIDTH,
                    reinterpret_cast<svBitVecVal*>(test_mask)
                );

                // Test VP read request
                s2h_pc_read_req(
                    reinterpret_cast<svBitVecVal*>(&addr),
                    DATA_WIDTH
                );

                //svSetScope(old_scope);
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    BFMInstance* find_instance(uint16_t id) {
        for (uint32_t i = 0; i < num_instances_; i++) {
            if (instances_[i].is_valid && instances_[i].id == id) {
                return &instances_[i];
            }
        }
        return nullptr;
    }

    // Add new member for data storage
    struct MemoryData {
        std::vector<uint32_t> data;  // Store data in 32-bit chunks
        MemoryData() : data() {}
        MemoryData(size_t size) : data(size, 0) {}
    };
    std::unordered_map<uint64_t, MemoryData> memory_map_;

    // Helper function to apply byte-level write mask
    void applyWriteMask(std::vector<uint32_t>& dest, const svBitVecVal* src, 
                       const svBitVecVal* mask, uint32_t size_in_bits) {
        const uint8_t* src_bytes = reinterpret_cast<const uint8_t*>(src);
        const uint8_t* mask_bits = reinterpret_cast<const uint8_t*>(mask);
        uint8_t* dest_bytes = reinterpret_cast<uint8_t*>(dest.data());
        
        // Calculate number of bytes to process
        uint32_t num_bytes = (size_in_bits + 7) / 8;
        
        // Process each byte
        for (uint32_t i = 0; i < num_bytes; i++) {
            // Check if this byte should be written (mask bit is 1)
            if (mask_bits[i / 8] & (1 << (i % 8))) {
                dest_bytes[i] = src_bytes[i];
            }
            // If mask bit is 0, keep original byte in dest
        }
    }

public:
    static BFMManager& getInstance() {
        static BFMManager instance;
        return instance;
    }

    void initBFM(uint16_t id, uint32_t page_size_in_bit, uint64_t addr_width, uint16_t data_width, uint32_t cont_line_per_channel, uint8_t merged_channel_num) {
        printf("C: Initializing BFM with ID=%d, page_size=%u bits, addr_width=%lu bits, data_width=%u bits, cont_line_per_channel=%u, merged_channel_num=%u\n", 
               id, page_size_in_bit, addr_width, data_width, cont_line_per_channel, merged_channel_num);

        svScope scope = svGetScope();
        if (!scope) {
            printf("Error: Could not get scope in initBFM\n");
            return;
        }

        if (num_instances_ >= MAX_BFM_NUM) {
            printf("Error: Maximum number of BFM instances reached\n");
            return;
        }

        BFMInstance* instance = &instances_[num_instances_++];
        instance->id = id;
        instance->scope = scope;
        instance->is_valid = true;

        if (!thread_running_) {
            thread_running_ = true;
            //test_thread_ = std::thread(&BFMManager::vp_test_function, this);
        }
    }

    void noStateResponse(int request_id) {
        printf("[C] No state response received - request_id=%d\n", request_id);
    }

    void resetPageTable(uint16_t id) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            printf("Error: Invalid BFM ID=%d in resetPageTable\n", id);
            return;
        }
        printf("C: Resetting page table for BFM ID=%d\n", id);
    }

    void pcWrite(uint16_t id, uint64_t addr, const svBitVecVal* data,
                uint32_t data_size_in_bits, const svBitVecVal* mask, char* done) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            printf("Error: Invalid BFM ID=%d in pcWrite\n", id);
            *done = 0;
            return;
        }

        printf("C: HW->VP Write request - BFM ID=%d, addr=0x%lx, size=%d bits\n",
               id, addr, data_size_in_bits);

        // Create or get existing memory location
        auto& mem_data = memory_map_[addr];
        if (mem_data.data.empty()) {
            mem_data.data.resize((data_size_in_bits+31)/32, 0);
        }

        // Apply masked write
        applyWriteMask(mem_data.data, data, mask, data_size_in_bits);
        
        *done = 1;
    }

    void pcRead(uint16_t id, uint64_t addr, uint32_t data_size_in_bits, svBitVecVal* data) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            printf("Error: Invalid BFM ID=%d in pcRead\n", id);
            return;
        }

        printf("C: HW->VP Read request - BFM ID=%d, addr=0x%lx, size=%d bits\n",
               id, addr, data_size_in_bits);

        // Clear output data first
        memset(data, 0, ((data_size_in_bits + 31)/32) * sizeof(uint32_t));

        // If address exists in map, copy the data
        auto it = memory_map_.find(addr);
        if (it != memory_map_.end()) {
            const size_t words = (data_size_in_bits + 31)/32;
            memcpy(data, it->second.data.data(), 
                   std::min(words * sizeof(uint32_t), 
                           it->second.data.size() * sizeof(uint32_t)));
        }
        // If address doesn't exist, data remains 0
    }

    void pcWriteResponse(uint16_t id, uint64_t addr) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            printf("Error: Invalid BFM ID=%d in pcWriteResponse\n", id);
            return;
        }
        printf("C: VP->HW Write response - BFM ID=%d, addr=0x%lx\n", id, addr);
    }

    void pcReadResponse(uint16_t id, uint64_t addr, uint32_t data_size_in_bits,
                       const svBitVecVal* data) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            printf("Error: Invalid BFM ID=%d in pcReadResponse\n", id);
            return;
        }
        printf("C: HW->VP Read response - BFM ID=%d, addr=0x%lx, size=%d bits\n",
               id, addr, data_size_in_bits);
        
        for (int i = 0; i < (data_size_in_bits + 31)/32; i++) {
            printf("%08x ", ((uint32_t*)data)[i]);
        }
        printf("\n");
    }

    void notifyPageReadCount(uint64_t page_id, uint32_t access_count) {
        printf("C: Page read count notification - page_id=0x%lx, count=%u\n",
               page_id, access_count);
    }

    void notifyPageWriteCount(uint64_t page_id, uint32_t access_count) {
        printf("C: Page write count notification - page_id=0x%lx, count=%u\n",
               page_id, access_count);
    }

    void switchPageResponse() {
        printf("C: Page switch response received\n");
    }

    void cleanup() {
        if (thread_running_) {
            thread_running_ = false;
            if (test_thread_.joinable()) {
                test_thread_.join();
            }
            printf("C: VP test thread cleaned up\n");
        }
    }
};

// Cleanup function - should be called when simulation ends
void __attribute__((destructor)) cleanup_bfm(void) {
    BFMManager::getInstance().cleanup();
}

// DPI-C exported functions
extern "C" {
    void init_bfm(
        uint16_t id,
        uint32_t page_size_in_bit,
        uint64_t addr_width,
        uint16_t data_width,
        uint32_t cont_line_per_channel,
        uint8_t merged_channel_num
    ) {
        BFMManager::getInstance().initBFM(id, page_size_in_bit, addr_width, data_width, cont_line_per_channel, merged_channel_num);
    }

    void reset_page_table(svBitVecVal* id_v) {
        uint16_t id = *((uint16_t*)id_v);
        BFMManager::getInstance().resetPageTable(id);
    }

    void h2s_pc_write(svBitVecVal* id_v, svBitVecVal* addr_v, svBitVecVal* data_v,
                      uint32_t data_size_in_bits, svBitVecVal* mask_v, char* done) {
        uint16_t id = *((uint16_t*)id_v);
        uint64_t addr = *((uint64_t*)addr_v);
        BFMManager::getInstance().pcWrite(id, addr, data_v, data_size_in_bits, mask_v, done);
    }

    void h2s_pc_read(svBitVecVal* id_v, svBitVecVal* addr_v, 
                     uint32_t data_size_in_bits,svBitVecVal* data_v)   {
        uint16_t id = *((uint16_t*)id_v);
        uint64_t addr = *((uint64_t*)addr_v);
        BFMManager::getInstance().pcRead(id, addr, data_size_in_bits, data_v);
    }

    void h2s_pc_write_resp(svBitVecVal* id_v, svBitVecVal* addr_v) {
        uint16_t id = *((uint16_t*)id_v);
        uint64_t addr = *((uint64_t*)addr_v);
        BFMManager::getInstance().pcWriteResponse(id, addr);
    }

    void h2s_pc_read_resp(svBitVecVal* id_v, svBitVecVal* addr_v,
                         uint32_t data_size_in_bits, svBitVecVal* data_v) {
        uint16_t id = *((uint16_t*)id_v);
        uint64_t addr = *((uint64_t*)addr_v);
        BFMManager::getInstance().pcReadResponse(id, addr, data_size_in_bits, data_v);
    }

    void h2s_notify_page_read_count(svBitVecVal* page_id_v, svBitVecVal* access_count_v) {
        uint64_t page_id = *((uint64_t*)page_id_v);
        uint32_t access_count = *((uint32_t*)access_count_v);
        BFMManager::getInstance().notifyPageReadCount(page_id, access_count);
    }

    void h2s_notify_page_write_count(svBitVecVal* page_id_v, svBitVecVal* access_count_v) {
        uint64_t page_id = *((uint64_t*)page_id_v);
        uint32_t access_count = *((uint32_t*)access_count_v);
        BFMManager::getInstance().notifyPageWriteCount(page_id, access_count);
    }

    void h2s_switch_page_response() {
        BFMManager::getInstance().switchPageResponse();
    }

    void h2s_no_state_response(int request_id) {
        BFMManager::getInstance().noStateResponse(request_id);
    }
} 
