`timescale 1ns/1ps

module hbm3_pc_bfm_tb;

    // Parameters
    localparam ADDR_WIDTH = 10;
    localparam DATA_WIDTH = 256;
    localparam DATA_BYTE_WIDTH = DATA_WIDTH/8;
    localparam PAGE_SIZE = 64*32*8;
    localparam HBM_ID = 1;
    localparam CHANNEL_ID = 7;
    localparam PC_ID = 1;

    localparam BFM_NUM = 2;

    // Clock and reset
    reg clk;
    reg rstn;

    // Write interface signals
    reg                         mm_wen [0: BFM_NUM-1];
    reg [64-1:0]               mm_waddr [0:BFM_NUM-1];
    reg [DATA_WIDTH-1:0]       mm_wdata [0:BFM_NUM-1];
    reg [DATA_BYTE_WIDTH-1:0]  mm_wmask [0:BFM_NUM-1];

    // Read interface signals
    reg                         mm_ren [0:BFM_NUM-1];
    reg [64-1:0]               mm_raddr [0:BFM_NUM-1];
    wire [DATA_WIDTH-1:0]      mm_rdata [0:BFM_NUM-1];
    wire                       mm_rvalid [0:BFM_NUM-1];

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;
    end

    // DUT instantiation
    hbm3_pc_bfm #(
        .HBM_ID(HBM_ID),
        .CHANNEL_ID(CHANNEL_ID),
        .PC_ID(PC_ID),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .PAGE_SIZE(PAGE_SIZE),
        .BASE_ADDR(64'h0000_0000_8000_0000)
    ) u_hbm3_pc_bfm0 (
        .mm_clk(clk),
        .mm_rstn(rstn),
        // Write port
        .mm_wen(mm_wen[0]),
        .mm_waddr(mm_waddr[0]),
        .mm_wdata(mm_wdata[0]),
        .mm_wmask(mm_wmask[0]),
        // Read port
        .mm_ren(mm_ren[0]),
        .mm_raddr(mm_raddr[0]),
        .mm_rdata(mm_rdata[0]),
        .mm_rvalid(mm_rvalid[0])
    );

    hbm3_pc_bfm #(
        .HBM_ID(HBM_ID),
        .CHANNEL_ID(CHANNEL_ID),
        .PC_ID(PC_ID+1),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .PAGE_SIZE(PAGE_SIZE),
        .BASE_ADDR(64'h0000_0000_8000_0000)
    ) u_hbm3_pc_bfm1 (
        .mm_clk(clk),
        .mm_rstn(rstn),
        // Write port
        .mm_wen(mm_wen[1]),
        .mm_waddr(mm_waddr[1]),
        .mm_wdata(mm_wdata[1]),
        .mm_wmask(mm_wmask[1]),
        // Read port
        .mm_ren(mm_ren[1]),
        .mm_raddr(mm_raddr[1]),
        .mm_rdata(mm_rdata[1]),
        .mm_rvalid(mm_rvalid[1])
    );

    // Test stimulus
    initial begin
        $readmemh("mem.hex", u_hbm3_pc_bfm0.memory);
        $readmemh("page.hex", u_hbm3_pc_bfm0.page_table);
        $readmemh("mem.hex", u_hbm3_pc_bfm1.memory);
        $readmemh("page.hex", u_hbm3_pc_bfm1.page_table);

        u_hbm3_pc_bfm0.h2s_no_state_response(188);

        // Initialize signals
        rstn <= 0;
        mm_wen[0] <= 0;
        mm_waddr[0] <= 0;
        mm_wdata[0] <= 0;
        mm_wmask[0] <= 0;
        mm_ren[0] <= 0;
        mm_raddr[0] <= 0;

        mm_wen[1] <= 0;
        mm_waddr[1] <= 0;
        mm_wdata[1] <= 0;
        mm_wmask[1] <= 0;
        mm_ren[1] <= 0;
        mm_raddr[1] <= 0;

        // Reset sequence
        #100 rstn <= 1;
        
        // Wait for initialization
        #100;

        // Test 1: Simple write followed by read
        // Write to address 0
        @(posedge clk);
        mm_wen[0] <= 1;
        mm_waddr[0] <= 64'h0000_0000_8000_0100;
        mm_wdata[0] <= {DATA_WIDTH{1'b1}};  // All 1's
        mm_wmask[0] <= {DATA_BYTE_WIDTH{1'b1}};  // All bytes enabled

        mm_wen[1] <= 1;
        mm_waddr[1] <= 64'h0000_0000_8000_8100;
        mm_wdata[1] <= {DATA_WIDTH{1'b1}};  // All 1's
        mm_wmask[1] <= {DATA_BYTE_WIDTH{1'b1}};  // All bytes enabled
        
        @(posedge clk);
        mm_wen[0] <= 0;
        mm_waddr[0] <= 64'h0000_0000_0000_0000;
        mm_wen[1] <= 0;
        mm_waddr[1] <= 64'h0000_0000_0000_8000;
        
        // Wait a few cycles
        repeat(5) @(posedge clk);
        
        // Read from address 0
        mm_ren[0] <= 1;
        mm_raddr[0] <= 64'h0000_0000_8000_0100;
        mm_ren[1] <= 1;
        mm_raddr[1] <= 64'h0000_0000_8000_8100;
        
        @(posedge clk);
        mm_ren[0] <= 0;
        mm_raddr[0] <= 64'h0000_0000_0000_0000;
        mm_ren[1] <= 0;
        mm_raddr[1] <= 64'h0000_0000_0000_0000;
        
        // Wait for read valid
        wait(mm_rvalid[0]);
        if(mm_rdata[0] === {DATA_WIDTH{1'b1}}) begin
            $display("INST 0: Test 1 PASSED: Read data matches written data");
        end else begin
            $display("INST 0: Test 1 FAILED: Read data mismatch, data: %h", mm_rdata[0]);
        end
        wait(mm_rvalid[1]);
        if(mm_rdata[1] === {DATA_WIDTH{1'b1}}) begin
            $display("INST 1: Test 1 PASSED: Read data matches written data");
        end else begin
            $display("INST 1: Test 1 FAILED: Read data mismatch, data: %h", mm_rdata[1]);
        end

        // Test 2: Write with byte enables
        @(posedge clk);
        mm_wen[0] <= 1;
        mm_waddr[0] <= 64'h0000_0000_8000_0020;
        mm_wdata[0] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5;
        mm_wmask[0] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        mm_wmask[0][0] <= 1'b1;  // Enable only first byte

        mm_wen[1] <= 1;
        mm_waddr[1] <= 64'h0000_0000_8000_8020;
        mm_wdata[1] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5;
        mm_wmask[1] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        mm_wmask[1][0] <= 1'b1;  // Enable only first byte
        
        @(posedge clk);
        mm_wen[0] <= 0;
        mm_waddr[0] <= 64'h0000_0000_0000_0000;
        mm_wen[1] <= 0;
        mm_waddr[1] <= 64'h0000_0000_0000_0000;
        
        repeat(5) @(posedge clk);
        
        mm_ren[0] <= 1;
        mm_raddr[0] <= 64'h0000_0000_8000_0020;
        mm_ren[1] <= 1;
        mm_raddr[1] <= 64'h0000_0000_8000_8020;
        
        @(posedge clk);
        mm_ren[0] <= 0;
        mm_raddr[0] <= 64'h0000_0000_0000_0000;
        mm_ren[1] <= 0;
        mm_raddr[1] <= 64'h0000_0000_0000_0000;
        
        wait(mm_rvalid[0]);
        if(mm_rdata[0][7:0] === 8'ha5) begin
            $display("INST 0: Test 2 PASSED: Byte enable working correctly");
        end else begin
            $display("INST 0: Test 2 FAILED: Byte enable not working, data: %h", mm_rdata[0]);
        end
        wait(mm_rvalid[1]);
        if(mm_rdata[1][7:0] === 8'ha5) begin
            $display("INST 1: Test 2 PASSED: Byte enable working correctly");
        end else begin
            $display("INST 1: Test 2 FAILED: Byte enable not working, data: %h", mm_rdata[1]);
        end

        // Add more test cases here...
        // Test 3: write write read read
        @(posedge clk);
        mm_wen[0] <= 1;
        mm_waddr[0] <= 64'h0000_0000_8000_0100;
        mm_wdata[0] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5;
        mm_wmask[0] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        mm_wen[1] <= 1;
        mm_waddr[1] <= 64'h0000_0000_8000_8100;
        mm_wdata[1] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5;
        mm_wmask[1] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        
        @(posedge clk);
        mm_wen[0] <= 0;
        mm_waddr[0] <= 64'h0000_0000_0000_0000;
        mm_wen[1] <= 0;
        mm_waddr[1] <= 64'h0000_0000_0000_0000;
        
        repeat(3) @(posedge clk);

        mm_wen[0] <= 1;
        mm_waddr[0] <= 64'h0000_0000_8000_0120;
        mm_wdata[0] <= 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a;
        mm_wmask[0] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        mm_wen[1] <= 1;
        mm_waddr[1] <= 64'h0000_0000_8000_8120;
        mm_wdata[1] <= 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a;
        mm_wmask[1] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        
        @(posedge clk);
        mm_wen[0] <= 0;
        mm_waddr[0] <= 64'h0000_0000_0000_0000;
        mm_wen[1] <= 0;
        mm_waddr[1] <= 64'h0000_0000_0000_0000;
        
        repeat(3) @(posedge clk);

        mm_wen[0] <= 1;
        mm_waddr[0] <= 64'h0000_0000_8000_0140;
        mm_wdata[0] <= 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a;
        mm_wmask[0] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        mm_wen[1] <= 1;
        mm_waddr[1] <= 64'h0000_0000_8000_8140;
        mm_wdata[1] <= 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a;
        mm_wmask[1] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        
        @(posedge clk);
        mm_wen[0] <= 0;
        mm_waddr[0] <= 64'h0000_0000_0000_0000;
        mm_wen[1] <= 0;
        mm_waddr[1] <= 64'h0000_0000_0000_0000;
        
        repeat(3) @(posedge clk);

        mm_wen[0] <= 1;
        mm_waddr[0] <= 64'h0000_0000_8000_0160;
        mm_wdata[0] <= 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a;
        mm_wmask[0] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        mm_wen[1] <= 1;
        mm_waddr[1] <= 64'h0000_0000_8000_8160;
        mm_wdata[1] <= 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a;
        mm_wmask[1] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        
        @(posedge clk);
        mm_wen[0] <= 0;
        mm_waddr[0] <= 64'h0000_0000_0000_0000;
        mm_wen[1] <= 0;
        mm_waddr[1] <= 64'h0000_0000_0000_0000;
        
        repeat(2) @(posedge clk);
        
        mm_ren[0] <= 1;
        mm_raddr[0] <= 64'h0000_0000_8000_0100;
        @(posedge clk);
        mm_ren[0] <= 0;
        mm_raddr[0] <= 64'h0000_0000_0000_0000;
        
        wait(mm_rvalid[0]);
        if(mm_rdata[0][DATA_WIDTH-1:0] === 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5) begin
            $display("INST 0: Test 3-1 PASSED: write/read working correctly");
        end else begin
            $display("INST 0: Test 3-1 FAILED: write/read not working, data: %h", mm_rdata[0]);
        end

        mm_ren[1] <= 1;
        mm_raddr[1] <= 64'h0000_0000_8000_8100;
        @(posedge clk);
        mm_ren[1] <= 0;
        mm_raddr[1] <= 64'h0000_0000_0000_0000;

        wait(mm_rvalid[1]);
        if(mm_rdata[1][DATA_WIDTH-1:0] === 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5) begin
            $display("INST 1: Test 3-1 PASSED: write/read working correctly");
        end else begin
            $display("INST 1: Test 3-1 FAILED: write/read not working, data: %h", mm_rdata[1]);
        end


        repeat(2) @(posedge clk);
        
        mm_ren[0] <= 1;
        mm_raddr[0] <= 64'h0000_0000_8000_0140;
        mm_ren[1] <= 1;
        mm_raddr[1] <= 64'h0000_0000_8000_8140;
        
        @(posedge clk);
        mm_ren[0] <= 0;
        mm_raddr[0] <= 64'h0000_0000_0000_0000;
        mm_ren[1] <= 0;
        mm_raddr[1] <= 64'h0000_0000_0000_0000;
        
        
        wait(mm_rvalid[0]);
        if(mm_rdata[0][DATA_WIDTH-1:0] === 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a) begin
            $display("INST 0: Test 3-2 PASSED: write/read working correctly");
        end else begin
            $display("INST 0: Test 3-2 FAILED: write/read not working");
        end
        wait(mm_rvalid[1]);
        if(mm_rdata[1][DATA_WIDTH-1:0] === 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a) begin
            $display("INST 1: Test 3-2 PASSED: write/read working correctly");
        end else begin
            $display("INST 1: Test 3-2 FAILED: write/read not working");
        end

        // Write to other page
        //fork 
            begin
                for (int i=0;i<40;i=i+1) begin
                    @(posedge clk);
                    mm_wen[0] <= 1;
                    mm_waddr[0] <= 64'h0000_0000_8000_0100 + i*32;
                    mm_wdata[0] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5 + i;
                    mm_wmask[0] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
                    
                    @(posedge clk);
                    mm_wen[0] <= 0;
                    mm_waddr[0] <= 64'h0000_0000_0000_0000;
                    
                    repeat(5) @(posedge clk);
                    
                    mm_ren[0] <= 1;
                    mm_raddr[0] <= 64'h0000_0000_8000_0100 + i*32;
                    
                    @(posedge clk);
                    mm_ren[0] <= 0;
                    mm_raddr[0] <= 64'h0000_0000_0000_0000;
                    
                    wait(mm_rvalid[0]);
                    if(mm_rdata[0] === (256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5 +i)) begin
                        $display("INST 0: Test 4-%0d PASSED: working correctly", i);
                    end else begin
                        $display("INST 0: Test 4-%0d FAILED: not working, data: %h", i, mm_rdata[0]);
                    end
                end
            end
            begin
                for (int i=0;i<40;i=i+1) begin
                    @(posedge clk);
                    mm_wen[1] <= 1;
                    mm_waddr[1] <= 64'h0000_0000_8000_8100 + i*32;
                    mm_wdata[1] <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5 + i;
                    mm_wmask[1] <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
                    
                    @(posedge clk);
                    mm_wen[1] <= 0;
                    mm_waddr[1] <= 64'h0000_0000_0000_0000;
                    
                    repeat(5) @(posedge clk);
                    
                    mm_ren[1] <= 1;
                    mm_raddr[1] <= 64'h0000_0000_8000_8100 + i*32;
                    
                    @(posedge clk);
                    mm_ren[1] <= 0;
                    mm_raddr[1] <= 64'h0000_0000_0000_0000;
                    
                    wait(mm_rvalid[1]);
                    if(mm_rdata[1] === (256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5 +i)) begin
                        $display("INST 1: Test 4-%0d PASSED: working correctly", i);
                    end else begin
                        $display("INST 1: Test 4-%0d FAILED: not working, data: %h", i, mm_rdata[1]);
                    end
                end
            end
        //join

        #1000;
        $finish;
    end

    // Optional: Dump waveforms
    initial begin
        //$dumpfile("hbm3_pc_bfm_tb.vcd");
        //$dumpvars(0, hbm3_pc_bfm_tb);
    end

endmodule 
