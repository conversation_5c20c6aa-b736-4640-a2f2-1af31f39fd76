`timescale 1ns/1ps

module hbm3_pc_bfm_tb;

    // Parameters
    localparam ADDR_WIDTH = 10;
    localparam DATA_WIDTH = 256;
    localparam DATA_BYTE_WIDTH = DATA_WIDTH/8;
    localparam PAGE_SIZE = 64*8;
    localparam HBM_ID = 1;
    localparam CHANNEL_ID = 7;
    localparam PC_ID = 1;

    // Clock and reset
    reg clk;
    reg rstn;

    // Write interface signals
    reg                         mm_wen;
    reg [64-1:0]       mm_waddr;
    reg [DATA_WIDTH-1:0]       mm_wdata;
    reg [DATA_BYTE_WIDTH-1:0]  mm_wmask;

    // Read interface signals
    reg                         mm_ren;
    reg [64-1:0]       mm_raddr;
    wire [DATA_WIDTH-1:0]      mm_rdata;
    wire                       mm_rvalid;

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;
    end

    // DUT instantiation
    hbm3_pc_bfm #(
        .HBM_ID(HBM_ID),
        .CHANNEL_ID(CHANNEL_ID),
        .PC_ID(PC_ID),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .BASE_ADDR(64'h80000000),
        .PAGE_SIZE(PAGE_SIZE)
    ) u_hbm3_pc_bfm (
        .mm_clk(clk),
        .mm_rstn(rstn),
        // Write port
        .mm_wen(mm_wen),
        .mm_waddr(mm_waddr),
        .mm_wdata(mm_wdata),
        .mm_wmask(mm_wmask),
        // Read port
        .mm_ren(mm_ren),
        .mm_raddr(mm_raddr),
        .mm_rdata(mm_rdata),
        .mm_rvalid(mm_rvalid)
    );

    // Test stimulus
    initial begin
        $readmemh("mem.hex", u_hbm3_pc_bfm.memory);
        $readmemh("page.hex", u_hbm3_pc_bfm.page_table);
        // Initialize signals
        rstn <= 0;
        mm_wen <= 0;
        mm_waddr <= 0;
        mm_wdata <= 0;
        mm_wmask <= 0;
        mm_ren <= 0;
        mm_raddr <= 0;

        // Reset sequence
        #100 rstn <= 1;
        
        // Wait for initialization
        #100;

        // Test 1: Simple write followed by read
        // Write to address 0
        @(posedge clk);
        mm_wen <= 1;
        mm_waddr <= 64'h80000120;
        mm_wdata <= {DATA_WIDTH{1'b1}};  // All 1's
        mm_wmask <= {DATA_BYTE_WIDTH{1'b1}};  // All bytes enabled
        
        @(posedge clk);
        mm_wen <= 0;
        
        // Wait a few cycles
        repeat(5) @(posedge clk);
        
        // Read from address 0
        mm_ren <= 1;
        mm_raddr <= 64'h80000120;
        
        @(posedge clk);
        mm_ren <= 0;
        
        // Wait for read valid
        wait(mm_rvalid);
        if(mm_rdata === {DATA_WIDTH{1'b1}}) begin
            $display("Test 1 PASSED: Read data matches written data");
        end else begin
            $display("Test 1 FAILED: Read data mismatch");
        end

        // Test 2: Write with byte enables
        @(posedge clk);
        mm_wen <= 1;
        mm_waddr <= 64'h80000140;
        mm_wdata <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5;
        mm_wmask <= {DATA_BYTE_WIDTH{1'b0}};  // No bytes enabled
        mm_wmask[0] <= 1'b1;  // Enable only first byte
        
        @(posedge clk);
        mm_wen <= 0;
        
        repeat(5) @(posedge clk);
        
        mm_ren <= 1;
        mm_raddr <= 64'h80000140;
        
        @(posedge clk);
        mm_ren <= 0;
        
        wait(mm_rvalid);
        if(mm_rdata[7:0] === 8'ha5) begin
            $display("Test 2 PASSED: Byte enable working correctly");
        end else begin
            $display("Test 2 FAILED: Byte enable not working");
        end

        // Add more test cases here...
        // Test 3: write write read read
        @(posedge clk);
        mm_wen <= 1;
        mm_waddr <= 64'h80000120;
        mm_wdata <= 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5;
        mm_wmask <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        
        @(posedge clk);
        mm_wen <= 0;
        
        repeat(3) @(posedge clk);

        mm_wen <= 1;
        mm_waddr <= 64'h80000140;
        mm_wdata <= 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a;
        mm_wmask <= {DATA_BYTE_WIDTH{1'b1}};  // No bytes enabled
        
        @(posedge clk);
        mm_wen <= 0;
        
        repeat(2) @(posedge clk);
        
        mm_ren <= 1;
        mm_raddr <= 64'h80000120;
        
        @(posedge clk);
        mm_ren <= 0;
        
        wait(mm_rvalid);
        if(mm_rdata[DATA_WIDTH-1:0] === 256'ha5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5) begin
            $display("Test 3-1 PASSED: write/read working correctly");
        end else begin
            $display("Test 3-1 FAILED: write/read not working");
        end

        repeat(2) @(posedge clk);
        
        mm_ren <= 1;
        mm_raddr <= 64'h80000140;
        
        @(posedge clk);
        mm_ren <= 0;
        
        wait(mm_rvalid);
        if(mm_rdata[DATA_WIDTH-1:0] === 256'h5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a5a) begin
            $display("Test 3-2 PASSED: write/read working correctly");
        end else begin
            $display("Test 3-2 FAILED: write/read not working");
        end

        #1000;
        $finish;
    end

    // Optional: Dump waveforms
    initial begin
        //$dumpfile("hbm3_pc_bfm_tb.vcd");
        //$dumpvars(0, hbm3_pc_bfm_tb);
    end

endmodule 
