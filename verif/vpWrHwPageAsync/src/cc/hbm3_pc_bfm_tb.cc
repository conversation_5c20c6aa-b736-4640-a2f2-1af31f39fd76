#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <thread>
#include <chrono>
#include <assert.h>
#include <vector>
#include <pthread.h>
#include <unistd.h>
#include <unordered_map>
#include <condition_variable>
#include <mutex>
#include "svdpi.h"
#include "vpi_user.h"

// Constants matching the Verilog parameters
#define ADDR_WIDTH 10
#define DATA_WIDTH 256
#define DATA_BYTE_WIDTH (DATA_WIDTH/8)
#define PAGE_SIZE (64*8)
#define MAX_BFM_NUM 16  // Maximum number of BFM instances we support

// Forward declarations of exported functions from Verilog (s2h = SystemVerilog to Host)
extern "C" {
    // Functions exported from SystemVerilog
    extern void s2h_pc_write_req(svBitVecVal* addr, svBitVecVal* data, 
                                int data_size_in_bits, svBitVecVal* mask);
    extern void s2h_pc_read_req(svBitVecVal* addr, int data_size_in_bits);
    extern void s2h_pc_read_data(svBitVecVal* data, int data_size_in_bits);
    extern void test_on();
    extern void test_done();
}

// Structure to store BFM instance information
struct BFMInstance {
    uint16_t id;
    svScope scope;
    bool is_valid;
};

bool cdone=false;

class BFMManager {
private:
    BFMManager() :  num_instances_(0) {}
    ~BFMManager() { cleanup(); }

    // Prevent copying
    BFMManager(const BFMManager&) = delete;
    BFMManager& operator=(const BFMManager&) = delete;


    // Add new member for data storage
    struct MemoryData {
        std::vector<uint32_t> data;  // Store data in 32-bit chunks
        MemoryData() : data() {}
        MemoryData(size_t size) : data(size, 0) {}
    };
    std::unordered_map<uint64_t, MemoryData> memory_map_;

    struct ReadResponse {
        bool valid;
        std::vector<uint32_t> data;
    };
    std::unordered_map<uint64_t, ReadResponse> read_responses_;

    // Helper function to apply byte-level write mask
    void applyWriteMask(std::vector<uint32_t>& dest, const svBitVecVal* src, 
                       const svBitVecVal* mask, uint32_t size_in_bits) {
        const uint8_t* src_bytes = reinterpret_cast<const uint8_t*>(src);
        const uint8_t* mask_bits = reinterpret_cast<const uint8_t*>(mask);
        uint8_t* dest_bytes = reinterpret_cast<uint8_t*>(dest.data());
        
        // Calculate number of bytes to process
        uint32_t num_bytes = (size_in_bits + 7) / 8;
        
        // Process each byte
        for (uint32_t i = 0; i < num_bytes; i++) {
            // Check if this byte should be written (mask bit is 1)
            if (mask_bits[i / 8] & (1 << (i % 8))) {
                dest_bytes[i] = src_bytes[i];
            }
            // If mask bit is 0, keep original byte in dest
        }
    }

public:
    static BFMManager& getInstance() {
        static BFMManager instance;
        return instance;
    }

    BFMInstance instances_[MAX_BFM_NUM];
    uint32_t num_instances_;

    
    BFMInstance* find_instance(uint16_t id) {
        for (uint32_t i = 0; i < num_instances_; i++) {
            if (instances_[i].is_valid && instances_[i].id == id) {
                return &instances_[i];
            }
        }
        return nullptr;
    }

    const int getNumber() const {
        return num_instances_;
    }

    void initBFM(uint16_t id, uint32_t page_size_in_bit, uint64_t addr_width, uint16_t data_width, uint32_t cont_line_per_channel, uint8_t merged_channel_num) {
        vpi_printf("[C] Initializing BFM with ID=%d, page_size=%u bits, addr_width=%lu bits, data_width=%u bits, cont_line_per_channel=%u, merged_channel_num=%u\n", 
               id, page_size_in_bit, addr_width, data_width, cont_line_per_channel, merged_channel_num);

        svScope scope = svGetScope();
        if (!scope) {
            vpi_printf("Error: Could not get scope in initBFM\n");
            return;
        }

        if (num_instances_ >= MAX_BFM_NUM) {
            vpi_printf("Error: Maximum number of BFM instances reached\n");
            return;
        }

        BFMInstance* instance = &instances_[num_instances_++];
        instance->id = id;
        instance->scope = scope;
        instance->is_valid = true;

        vpi_printf("[C] End of initBFM\n");

    }

    void noStateResponse(int request_id) {
        vpi_printf("[C] No state response received - request_id=%d\n", request_id);
    }

    void resetPageTable(uint16_t id) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            vpi_printf("Error: Invalid BFM ID=%d in resetPageTable\n", id);
            return;
        }
        vpi_printf("[C] Resetting page table for BFM ID=%d\n", id);
    }

    void pcWrite(uint16_t id, uint64_t addr, const svBitVecVal* data,
                uint32_t data_size_in_bits, const svBitVecVal* mask, char* done) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            vpi_printf("Error: Invalid BFM ID=%d in pcWrite\n", id);
            *done = 0;
            return;
        }

        vpi_printf("[C] HW->VP Write request - BFM ID=%d, addr=0x%lx, size=%d bits\n",
               id, addr, data_size_in_bits);

        // Create or get existing memory location
        auto& mem_data = memory_map_[addr];
        if (mem_data.data.empty()) {
            mem_data.data.resize((data_size_in_bits+31)/32, 0);
        }

        // Apply masked write
        applyWriteMask(mem_data.data, data, mask, data_size_in_bits);
        
        *done = 1;
    }

    void pcRead(uint16_t id, uint64_t addr, uint32_t data_size_in_bits, svBitVecVal* data) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            vpi_printf("Error: Invalid BFM ID=%d in pcRead\n", id);
            return;
        }

        vpi_printf("[C] HW->VP Read request - BFM ID=%d, addr=0x%lx, size=%d bits\n",
               id, addr, data_size_in_bits);

        // Clear output data first
        memset(data, 0, ((data_size_in_bits + 31)/32) * sizeof(uint32_t));

        // If address exists in map, copy the data
        auto it = memory_map_.find(addr);
        if (it != memory_map_.end()) {
            const size_t words = (data_size_in_bits + 31)/32;
            memcpy(data, it->second.data.data(), 
                   std::min(words * sizeof(uint32_t), 
                           it->second.data.size() * sizeof(uint32_t)));
        }
        // If address doesn't exist, data remains 0
    }

    void pcWriteResponse(uint16_t id, uint64_t addr) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            vpi_printf("Error: Invalid BFM ID=%d in pcWriteResponse\n", id);
            return;
        }
        vpi_printf("[C] VP->HW Write response - BFM ID=%d, addr=0x%lx\n", id, addr);
    }

    void pcReadResponse(uint16_t id, uint64_t addr, uint32_t data_size_in_bits,
                       const svBitVecVal* data) {
        BFMInstance* instance = find_instance(id);
        if (!instance) {
            vpi_printf("Error: Invalid BFM ID=%d in pcReadResponse\n", id);
            return;
        }
        vpi_printf("[C] HW->VP Read response - BFM ID=%d, addr=0x%lx, size=%d bits\n",
               id, addr, data_size_in_bits);

    }

    void notifyPageReadCount(uint64_t page_id, uint32_t access_count) {
        vpi_printf("[C] Page read count notification - page_id=0x%lx, count=%u\n",
               page_id, access_count);
    }

    void notifyPageWriteCount(uint64_t page_id, uint32_t access_count) {
        vpi_printf("[C] Page write count notification - page_id=0x%lx, count=%u\n",
               page_id, access_count);
    }

    void switchPageResponse() {
        vpi_printf("[C] Page switch response received\n");
    }

    // Add these methods to BFMManager
    void setupReadResponse(uint64_t addr) {
        read_responses_[addr].valid = false;
        read_responses_[addr].data.clear();
    }

    void handleReadResponse(uint64_t addr, uint32_t data_size_in_bits, const svBitVecVal* data) {
        const size_t words = (data_size_in_bits + 31)/32;
        read_responses_[addr].data.resize(words);
        memcpy(read_responses_[addr].data.data(), data, words * sizeof(uint32_t));
        read_responses_[addr].valid = true;
    }

    bool getReadResponse(uint64_t addr, svBitVecVal* data) {
        if (!read_responses_[addr].valid) {
            vpi_printf("[C] can't find the addr %lx\n", addr);
            return false;
        }
        
        vpi_printf("[C] find the addr %lx\n", addr);
        const size_t words = (DATA_WIDTH + 31)/32;
        memcpy(data, read_responses_[addr].data.data(), words * sizeof(uint32_t));
        //read_responses_.erase(addr);
        return true;
    }

    void cleanup() {
        // Join all test threads
        read_responses_.clear();
        vpi_printf("[C] VP test threads cleaned up\n");
    }
};

// Cleanup function - should be called when simulation ends
static void __attribute__((destructor)) cleanup_bfm(void) {
    // The singleton's destructor will handle cleanup of read_responses_
    // Just print a message to indicate cleanup
    vpi_printf("[C] VP cleanup_bfm called\n");
}


// DPI-C exported functions
extern "C" {
    void init_bfm(
        uint16_t id,
        uint32_t page_size_in_bit,
        uint64_t addr_width,
        uint16_t data_width,
        uint32_t cont_line_per_channel,
        uint8_t merged_channel_num
    ) {
        vpi_printf("[C] init_bfm called with id=%d\n", id);
        BFMManager::getInstance().initBFM(id, page_size_in_bit, addr_width, data_width, cont_line_per_channel, merged_channel_num);
    }

    void vp_write_test(unsigned int id, svBitVecVal* addr, svBitVecVal* data, svBitVecVal* mask) {
        uint16_t local_id = id;
        vpi_printf("[C] VP write test function called with id=%d\n", local_id);

        BFMManager * manager = &BFMManager::getInstance();
        BFMInstance * instance = manager->find_instance(local_id);

        vpi_printf("[C] VP write test function at scope=%s \n", svGetNameFromScope(instance->scope));
        svSetScope(instance->scope);

        //usleep(1000);
        s2h_pc_write_req(
            addr,
            data,
            DATA_WIDTH,
            mask
        );

        vpi_printf("[C] VP write test function done with id=%d\n", local_id);
        return ;
    }

    void vp_read_test(unsigned int id, svBitVecVal* addr) {
        uint16_t local_id = id;
        vpi_printf("[C] VP read test function called with id=%d\n", local_id);

        BFMManager * manager = &BFMManager::getInstance();
        BFMInstance * instance = manager->find_instance(local_id);

        vpi_printf("[C] VP read test function at scope=%s \n", svGetNameFromScope(instance->scope));
        svSetScope(instance->scope);

        uint64_t read_addr = *reinterpret_cast<uint64_t*>(addr);
        
        // Setup read response tracking
        manager->setupReadResponse(read_addr);

        s2h_pc_read_req(
            addr,
            DATA_WIDTH
        );

        vpi_printf("[C] VP read test function done with id=%d\n", local_id);

        return ;
    }

    void vp_read_resp(unsigned int id, svBitVecVal* addr, svBitVecVal* data) {
        uint16_t local_id = id;
        vpi_printf("[C] VP read test function called with id=%d\n", local_id);

        BFMManager * manager = &BFMManager::getInstance();
        BFMInstance * instance = manager->find_instance(local_id);

        vpi_printf("[C] VP read test function at scope=%s \n", svGetNameFromScope(instance->scope));
        svSetScope(instance->scope);

        uint64_t read_addr = *reinterpret_cast<uint64_t*>(addr);

        manager->getReadResponse(read_addr, data);
        
    }


    void reset_page_table(svBitVecVal* id_v) {
        uint16_t id = *((uint16_t*)id_v);
        BFMManager::getInstance().resetPageTable(id);
    }

    void h2s_pc_write(svBitVecVal* id_v, svBitVecVal* addr_v, svBitVecVal* data_v,
                      uint32_t data_size_in_bits, svBitVecVal* mask_v, char* done) {
        uint16_t id = *((uint16_t*)id_v);
        uint64_t addr = *((uint64_t*)addr_v);
        BFMManager::getInstance().pcWrite(id, addr, data_v, data_size_in_bits, mask_v, done);
    }

    void h2s_pc_read(svBitVecVal* id_v, svBitVecVal* addr_v, 
                     uint32_t data_size_in_bits,svBitVecVal* data_v)   {
        uint16_t id = *((uint16_t*)id_v);
        uint64_t addr = *((uint64_t*)addr_v);
        BFMManager::getInstance().pcRead(id, addr, data_size_in_bits, data_v);
    }

    std::condition_variable write_cv;
    std::mutex write_cv_mutex;
    std::condition_variable read_cv;
    std::mutex read_cv_mutex;

    void h2s_pc_write_resp(svBitVecVal* id_v, svBitVecVal* addr_v) {
        uint16_t id = *((uint16_t*)id_v);
        uint64_t addr = *((uint64_t*)addr_v);
        BFMManager::getInstance().pcWriteResponse(id, addr);

        std::unique_lock<std::mutex> lock(write_cv_mutex);
        write_cv.notify_one();
        //vpi_printf("[C] h2s_pc_write_resp called\n");
    }

    void h2s_pc_read_resp(svBitVecVal* id_v, svBitVecVal* addr_v,
                         uint32_t data_size_in_bits, svBitVecVal* data_v) {
        uint16_t id = *((uint16_t*)id_v);
        uint64_t addr = *((uint64_t*)addr_v);
        BFMManager* manager = &BFMManager::getInstance();
        BFMInstance* instance = manager->find_instance(id);
        if (!instance) {
            vpi_printf("Error: Invalid BFM ID=%d in pcReadResponse\n", id);
            return;
        }

        vpi_printf("[C] HW->VP Read response - BFM ID=%d, addr=0x%lx, size=%d bits\n",
               id, addr, data_size_in_bits);

        // Handle response in BFMManager
        manager->handleReadResponse(addr, data_size_in_bits, data_v);
        
        // Debug print
        vpi_printf("[C] h2s_pc_read_resp called\n");
        for (int i = 0; i < (data_size_in_bits + 31)/32; i++) {
            vpi_printf("%08x ", ((uint32_t*)data_v)[i]);
        }
        vpi_printf("\n");        

        std::unique_lock<std::mutex> lock(read_cv_mutex);
        read_cv.notify_one();
    }

    void h2s_notify_page_read_count(svBitVecVal* page_id_v, svBitVecVal* access_count_v) {
        uint64_t page_id = *((uint64_t*)page_id_v);
        uint32_t access_count = *((uint32_t*)access_count_v);
        BFMManager::getInstance().notifyPageReadCount(page_id, access_count);
    }

    void h2s_notify_page_write_count(svBitVecVal* page_id_v, svBitVecVal* access_count_v) {
        uint64_t page_id = *((uint64_t*)page_id_v);
        uint32_t access_count = *((uint32_t*)access_count_v);
        BFMManager::getInstance().notifyPageWriteCount(page_id, access_count);
    }

    void h2s_switch_page_response() {
        BFMManager::getInstance().switchPageResponse();
    }

    void h2s_no_state_response(int request_id) {
        BFMManager::getInstance().noStateResponse(request_id);
    }

    void start_ctest() {
        svSetScope(svGetScopeFromName("hbm3_pc_bfm_tb"));
        test_on();
        std::thread ctest_thread([&]() {
            BFMManager * manager = &BFMManager::getInstance();
            int inst_num = manager->getNumber();
            while (inst_num < 1) {
                usleep(1000);
                inst_num = manager->getNumber();
            }
            for (int i=0; i<2; i++) {
                BFMInstance * instance = &(manager->instances_[i]);

                for (int j=0; j<20; j++) {
                    vpi_printf("[C] VP write/read test function at scope=%s %dth\n", svGetNameFromScope(instance->scope), j);
                    svSetScope(instance->scope);

                    uint32_t addr[2] = {0x80000000+i*32*32+j*0x20, 0x00000000};
                    uint32_t wdata[8] = {0x12345600+j, 0x90abcdef, 0x12345678, 0x90abcdef, 0x12345678, 0x90abcdef, 0x12345678, 0x90abcdef};
                    uint32_t mask[1] = {0xffffffff};

                    s2h_pc_write_req(
                        (svBitVecVal*)&addr,
                        (svBitVecVal*)wdata,
                        DATA_WIDTH,
                        (svBitVecVal*)&mask
                    );

                    std::unique_lock<std::mutex> wlock(write_cv_mutex);
                    write_cv.wait(wlock);

                    //vpi_printf("[C] get write response for addr %lx\n", addr[0]);

                    s2h_pc_read_req(
                        (svBitVecVal*)&addr,
                        DATA_WIDTH
                    );

                    std::unique_lock<std::mutex> rlock(read_cv_mutex);
                    read_cv.wait(rlock);
                    vpi_printf("[C] read_cv is notified\n");

                    svBitVecVal* rdata = (svBitVecVal*)malloc(DATA_WIDTH/8);
                    uint64_t read_addr = *reinterpret_cast<uint64_t*>(addr);
                    manager->getReadResponse(read_addr, rdata);

                    for (int i=0; i<DATA_WIDTH/8; i++) {
                        vpi_printf("%02x ", ((uint8_t*)rdata)[i]);
                    }
                    vpi_printf("\n");

                    for(int i=0; i<DATA_WIDTH/32; i++) {
                        if (rdata[i] != wdata[i]) {
                            vpi_printf("[C] VP read test failed with id=%d i=%d, data=%08x, expected=%08x\n", instance->id, i, rdata[i], wdata[i]);
                            return;
                        }
                    }

                    vpi_printf("[C] VP write/read test function done with id=%d\n", instance->id);
                }
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            svSetScope(svGetScopeFromName("hbm3_pc_bfm_tb"));
            test_done();
        });
        ctest_thread.detach();
    }

} 
