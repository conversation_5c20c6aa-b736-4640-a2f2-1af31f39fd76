`timescale 1ns/1ps

`define DEBUG 1

module hbm3_pc_bfm_tb;

    // Parameters
    localparam ADDR_WIDTH = 10;
    localparam DATA_WIDTH = 256;
    localparam DATA_BYTE_WIDTH = DATA_WIDTH/8;
    localparam PAGE_SIZE = 256*32;
    localparam [9:0] HBM_ID = 1;
    localparam [4:0] CHANNEL_ID = 7;
    localparam [0:0] PC_ID = 1;

    localparam BFM_NUM = 2;

    // Clock and reset
    reg clk;
    reg rstn;

    // Write interface signals
    reg                         mm_wen [0: BFM_NUM-1];
    reg [64-1:0]               mm_waddr [0:BFM_NUM-1];
    reg [DATA_WIDTH-1:0]       mm_wdata [0:BFM_NUM-1];
    reg [DATA_BYTE_WIDTH-1:0]  mm_wmask [0:BFM_NUM-1];

    // Read interface signals
    reg                         mm_ren [0:BFM_NUM-1];
    reg [64-1:0]               mm_raddr [0:BFM_NUM-1];
    wire [DATA_WIDTH-1:0]      mm_rdata [0:BFM_NUM-1];
    wire                       mm_rvalid [0:BFM_NUM-1];

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;
    end

    // DUT instantiation
    genvar inst_num;
    generate
        for (inst_num = 0; inst_num < BFM_NUM; inst_num = inst_num + 1) begin :geninst
            hbm3_pc_bfm #(
                .HBM_ID(HBM_ID),
                .CHANNEL_ID(CHANNEL_ID+inst_num),
                .PC_ID(PC_ID),
                .ADDR_WIDTH(ADDR_WIDTH),
                .DATA_WIDTH(DATA_WIDTH),
                .PAGE_SIZE(PAGE_SIZE),
                .BASE_ADDR(64'h0000_0000_8000_0000)
            ) u_hbm3_pc_bfm (
                .mm_clk(clk),
                .mm_rstn(rstn),
                // Write port
                .mm_wen(mm_wen[inst_num]),
                .mm_waddr(mm_waddr[inst_num]),
                .mm_wdata(mm_wdata[inst_num]),
                .mm_wmask(mm_wmask[inst_num]),
                // Read port
                .mm_ren(mm_ren[inst_num]),
                .mm_raddr(mm_raddr[inst_num]),
                .mm_rdata(mm_rdata[inst_num]),
                .mm_rvalid(mm_rvalid[inst_num])
            );
        end
    endgenerate

    bit [15:0] id = {PC_ID, CHANNEL_ID, HBM_ID};

    bit [DATA_WIDTH-1:0] wdata [0:BFM_NUM-1];
    bit [DATA_WIDTH-1:0] rdata [0:BFM_NUM-1];
    bit [DATA_BYTE_WIDTH-1:0] wmask={DATA_BYTE_WIDTH{1'b1}};
    bit [63:0] addr [0:BFM_NUM-1];

    event initmem;
    event resetev;

    generate
        for(genvar i = 0; i < BFM_NUM; i++) begin
            initial begin
                $readmemh("mem.hex", geninst[i].u_hbm3_pc_bfm.memory);
                $readmemh("page.hex", geninst[i].u_hbm3_pc_bfm.page_table);
                $display("[SV] Memory and page table initialized %d", i);
                -> initmem;
            end
        end
    endgenerate

    initial begin
        @(initmem);

        #100;

        for (int i = 0; i < BFM_NUM; i++) begin
            mm_wen[i] <= 0;
            mm_waddr[i] <= 0;
            mm_wdata[i] <= 0;
            mm_wmask[i] <= 0;
            mm_ren[i] <= 0;
            mm_raddr[i] <= 0;
        end

        $display("[SV] Reset sequence");

        // Reset sequence
        rstn <= 0;
        #100;
        rstn <= 1;

        #100;

        -> resetev;
    end

    import "DPI-C" function void start_ctest();

    localparam ST_DPI_TEST_IDLE = 0;
    localparam ST_DPI_TEST_ON = 1;
    localparam ST_DPI_TEST_DONE = 2;
    localparam ST_DPI_TEST_FINISH = 3;

    bit [1:0] dpi_test_state = ST_DPI_TEST_IDLE;
    bit [1:0] next_dpi_test_state;

    always @(posedge clk) begin
        if (!rstn) begin
            dpi_test_state <= ST_DPI_TEST_IDLE;
        end else begin
            dpi_test_state <= next_dpi_test_state;
        end
    end

    bit c_teston = 1'b0;
    bit c_testdone = 1'b0;

    bit teston = 1'b0;
    bit testdone = 1'b0;

    reg [9:0] cnt = 0;

    export "DPI-C" function test_on;
    export "DPI-C" function test_done;

    function void test_on();
        c_teston = 1'b1;
        $display("@%0t [SV] Test on", $time);
    endfunction

    function void test_done();
        c_testdone = 1'b1;
        $display("@%0t [SV] set testdone", $time);
    endfunction

    always @(posedge clk) begin
        testdone <= c_testdone;
        teston <= c_teston;
        if (dpi_test_state != next_dpi_test_state) begin
            cnt <= 0;
        end else begin
            cnt <= cnt + 1;
        end
    end

    always @(dpi_test_state or teston or testdone or cnt) begin
        case (dpi_test_state)
            ST_DPI_TEST_IDLE: begin
                if ( teston == 1'b1 ) begin
                    next_dpi_test_state = ST_DPI_TEST_ON;
                end else begin
                    next_dpi_test_state = ST_DPI_TEST_IDLE;
                end
            end
            ST_DPI_TEST_ON: begin
                if ( testdone == 1'b1 ) begin
                    next_dpi_test_state = ST_DPI_TEST_DONE;
                end else begin
                    next_dpi_test_state = ST_DPI_TEST_ON;
                end
            end
            ST_DPI_TEST_DONE: begin
                if (cnt >= 100) begin
                    next_dpi_test_state = ST_DPI_TEST_FINISH;
                end else begin
                    next_dpi_test_state = ST_DPI_TEST_DONE;
                end
            end
            ST_DPI_TEST_FINISH: begin
                next_dpi_test_state = ST_DPI_TEST_IDLE;
            end
        endcase
    end

    always @(posedge clk) begin
        if (dpi_test_state == ST_DPI_TEST_FINISH) begin
            $display("@%0t [SV] All test done", $time);
            $finish;
        end
    end

    initial begin
        $timeformat(-9, 2, "ns", 10);
`ifdef DUMPVCD
        $dumpfile("dump.vcd");
        $dumpvars(0, hbm3_pc_bfm_tb);
`endif
        @(resetev);
        #1000;
        start_ctest();
    end



endmodule 
