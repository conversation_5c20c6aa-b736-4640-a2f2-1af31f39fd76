#!/bin/env python3

import random

def generate_hex_files():
    # Generate mem.hex with randomized prefix and incrementing suffix
    with open("mem.hex", "w") as file:
        for i in range(1, 1025):
            # Generate 60 random hex characters for the prefix
            random_prefix = ''.join(random.choice('0123456789abcdef') for _ in range(60))
            
            # Convert the number to hex (without '0x'), right-aligned and zero-padded to 4 characters
            value_suffix = format(i, '04x')
            
            # Combine to create the full 64-character hex string
            hex_line = random_prefix + value_suffix
            
            # Write to file with newline
            file.write(hex_line + "\n")
    
    print("Generated mem.hex with randomized prefixes and values 1-1024")
    
    # Generate page.hex with 1024 identical lines
    with open("page.hex", "w") as file:
        for _ in range(512):
            file.write("8000000000000000\n")
    
    print("Generated 512 lines of 8000000000000000 in page.hex")

if __name__ == "__main__":
    generate_hex_files()
