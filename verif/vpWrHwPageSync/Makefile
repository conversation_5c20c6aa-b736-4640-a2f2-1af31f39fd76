# Common variables
SVLIB_NAME = libhbm3.so
SRC_DIR = ../../rtl
TB_DIR = ./src
SRCS = ${SRC_DIR}/hbm3_pc_bfm.sv
CTB_SRC = ${TB_DIR}/cc/hbm3_pc_bfm_tb.cc
SVTB_SRC = ${TB_DIR}/sv/hbm3_pc_bfm_tb.sv
VCD = 0
ifeq (${VCD}, 1)
	DUMP_VCD = +define+DUMP_VCD
else
	DUMP_VCD =
endif
DEBUG_SW = 1
ifeq (${DEBUG_SW}, 1)
	DEBUG_SW = -DDEBUG_SW
else
	DEBUG_SW =
endif
DEBUG_HW = 1
ifeq (${DEBUG_HW}, 1)
	DEBUG_HW = +define+DEBUG+VP_TEST
else
	DEBUG_HW =
endif
HW_DEFINE_OPTS = ${DUMP_VCD} ${DEBUG_HW}

# Help target
help:
	@echo "Available targets:"
	@echo "  help      - Show this help message"
	@echo "  genmem    - Generate memory hex files"
	@echo ""
	@echo "VCS targets:"
	@echo "  vcs_comp  - Compile design with VCS"
	@echo "  vcs_run   - Run simulation with VCS"
	@echo "  vcs_grun  - Run simulation with VCS GUI"
	@echo ""
	@echo "Xcelium targets:"
	@echo "  xlm_comp  - Compile design with Xcelium"
	@echo "  xlm_run   - Run simulation with Xcelium"
	@echo "  xlm_grun  - Run simulation with Xcelium GUI"
	@echo ""
	@echo "Clean targets:"
	@echo "  clean     - Clean simulation files"
	@echo "  cleanall  - Clean all generated files"

# Common DPI library compilation
$(SVLIB_NAME) dpi: ${CTB_SRC}
	g++ -o ${SVLIB_NAME} ${CTB_SRC} ${DEBUG_SW} -I/opt/synopsys/vcs/V-2023.12-SP1/include -shared -fPIC -pthread -g

# Memory generation
genmem:
	./scripts/genmem.py

# Create simulation directories and link hex files
create_dirs: genmem
	mkdir -p sim/vcs sim/xlm
	cd sim/vcs && ln -sf ../../*.hex .
	cd sim/xlm && ln -sf ../../*.hex .

# VCS targets
vcs_comp: create_dirs ${SRCS} ${SVTB_SRC}
	cd sim/vcs && vcs -full64 -kdb -sverilog -debug_access+all ${HW_DEFINE_OPTS} ../../${SRCS} ../../${SVTB_SRC} -CFLAGS -g

vcs_run: ${SVLIB_NAME}
	cd sim/vcs && ./simv -sv_lib ../../$(basename ${SVLIB_NAME}) -ucli -do ../../scripts/vsim.tcl -ucli2Proc -l vcs.log

vcs_grun: ${SVLIB_NAME}
	cd sim/vcs && ./simv -gui -sv_lib ../../$(basename ${SVLIB_NAME}) -ucli -do ../../scripts/vsim.tcl -l vcs.log

# Xcelium targets
xlm_comp: create_dirs ${SRCS} ${SVTB_SRC}
	cd sim/xlm && xrun -c -sv ../../${SRCS} ../../${SVTB_SRC} ${HW_DEFINE_OPTS} -access rwc -linedebug

xlm_run: ${SVLIB_NAME}
	cd sim/xlm && xrun -R -sv_lib ../../${SVLIB_NAME} -input ../../scripts/xsim.tcl

xlm_grun: ${SVLIB_NAME}
	cd sim/xlm && xrun -R -gui -sv_lib ../../${SVLIB_NAME} -input ../../scripts/xsim.tcl -l xlm.log

# Clean targets
cleanall:
	rm -rf sim/vcs/* sim/xlm/* verdiLog csrc simv simv.daidir ${SVLIB_NAME} *.hex *.log verdi_config_file vc_hdrs.h ucli.key sysBusyPLog *.fsdb xcelium.d novas.*

clean:
	rm -rf sim/vcs/* sim/xlm/* verdiLog csrc simv.daidir simv *.fsdb *.log verdi_config_file sysBusyPLog xcelium.d *.so

.PHONY: help vcs_comp vcs_run vcs_grun xlm_comp xlm_run xlm_grun clean genmem cleanall create_dirs

# Set default target to help
.DEFAULT_GOAL := help
