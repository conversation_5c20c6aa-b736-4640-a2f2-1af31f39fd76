#ifndef SFIFO_HFIFO_CLIENT_H
#define SFIFO_HFIFO_CLIENT_H

#include <queue>
#include <mutex>
#include <vector>
#include <functional>
#include <stdint.h>
#include "svdpi.h"

/**
 * @brief Generic Software FIFO to Hardware FIFO (SFIFO-HFIFO) Client
 * 
 * This class provides a reusable abstraction for the SFIFO-HFIFO mechanism where:
 * - Software side stores requests in local FIFOs
 * - Hardware side polls the software side for requests
 * - Supports batch operations for better performance
 * 
 * @tparam RequestType The type of request data structure
 */
template<typename RequestType>
class SfifoHfifoClient {
public:
    /**
     * @brief Callback function type for sending requests to hardware
     * @param requests Vector of requests to send
     * @param max_count Maximum number of requests hardware can accept
     * @return Number of requests actually sent
     */
    using SendCallback = std::function<int(const std::vector<RequestType>&, int)>;

    /**
     * @brief Constructor
     * @param send_callback Function to call when hardware polls for requests
     * @param max_batch_size Maximum number of requests to send in one batch
     */
    SfifoHfifoClient(SendCallback send_callback, int max_batch_size = 1)
        : send_callback_(send_callback), max_batch_size_(max_batch_size) {}

    /**
     * @brief Add a request to the software FIFO
     * @param request The request to add
     */
    void pushRequest(const RequestType& request) {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        request_fifo_.push(request);
    }

    /**
     * @brief Hardware polling function - called by hardware to get requests
     * @param hw_available_space Number of requests hardware can accept
     * @return Number of requests sent to hardware
     */
    int pollRequests(int hw_available_space) {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        
        if (request_fifo_.empty()) {
            return 0;
        }

        // Calculate how many requests to send (optimize for batch)
        int batch_size = std::min({
            static_cast<int>(request_fifo_.size()),
            hw_available_space,
            max_batch_size_
        });

        if (batch_size == 0) {
            return 0;
        }

        // Prepare batch of requests
        std::vector<RequestType> batch;
        batch.reserve(batch_size);
        
        for (int i = 0; i < batch_size; ++i) {
            batch.push_back(request_fifo_.front());
            request_fifo_.pop();
        }

        // Send to hardware
        return send_callback_(batch, hw_available_space);
    }

    /**
     * @brief Get current FIFO size
     * @return Number of pending requests
     */
    size_t size() const {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        return request_fifo_.size();
    }

    /**
     * @brief Check if FIFO is empty
     * @return true if empty, false otherwise
     */
    bool empty() const {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        return request_fifo_.empty();
    }

    /**
     * @brief Clear all pending requests
     */
    void clear() {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        std::queue<RequestType> empty_queue;
        request_fifo_.swap(empty_queue);
    }

private:
    std::queue<RequestType> request_fifo_;
    mutable std::mutex fifo_mutex_;
    SendCallback send_callback_;
    int max_batch_size_;
};

/**
 * @brief Write request data structure
 */
struct WriteRequest {
    uint64_t addr;
    std::vector<uint32_t> data;
    std::vector<uint32_t> mask;
    
    WriteRequest() = default;
    WriteRequest(uint64_t a, const std::vector<uint32_t>& d, const std::vector<uint32_t>& m)
        : addr(a), data(d), mask(m) {}
};

/**
 * @brief Read request data structure  
 */
struct ReadRequest {
    uint64_t addr;
    
    ReadRequest() = default;
    ReadRequest(uint64_t a) : addr(a) {}
};

/**
 * @brief Specialized SFIFO-HFIFO client for write operations
 */
using WriteClient = SfifoHfifoClient<WriteRequest>;

/**
 * @brief Specialized SFIFO-HFIFO client for read operations
 */
using ReadClient = SfifoHfifoClient<ReadRequest>;

#endif // SFIFO_HFIFO_CLIENT_H
