#ifndef SFIFO_HFIFO_WRAPPER_H
#define SFIFO_HFIFO_WRAPPER_H

#include "sfifo_hfifo_client.h"
#include "svdpi.h"
#include "vpi_user.h"
#include <memory>
#include <unordered_map>

/**
 * @brief Integrated SFIFO-HFIFO wrapper that combines software and hardware sides
 * 
 * This class provides a complete abstraction for the SFIFO-HFIFO mechanism,
 * managing both software FIFOs and DPI-C interfaces to hardware.
 */
class SfifoHfifoWrapper {
public:
    /**
     * @brief Constructor
     * @param scope_name SystemVerilog scope name for DPI-C calls
     * @param max_batch_size Maximum batch size for transfers
     */
    SfifoHfifoWrapper(const std::string& scope_name, int max_batch_size = 4);
    
    /**
     * @brief Destructor
     */
    ~SfifoHfifoWrapper();
    
    /**
     * @brief Add a write request to the software FIFO
     * @param addr Address to write
     * @param data Data to write
     * @param mask Write mask
     */
    void addWriteRequest(uint64_t addr, const std::vector<uint32_t>& data, 
                        const std::vector<uint32_t>& mask);
    
    /**
     * @brief Add a read request to the software FIFO
     * @param addr Address to read
     */
    void addReadRequest(uint64_t addr);
    
    /**
     * @brief Get current write FIFO size
     * @return Number of pending write requests
     */
    size_t getWriteFifoSize() const;
    
    /**
     * @brief Get current read FIFO size
     * @return Number of pending read requests
     */
    size_t getReadFifoSize() const;
    
    /**
     * @brief Clear all pending requests
     */
    void clearAllRequests();
    
    // DPI-C callback functions (called by hardware)
    /**
     * @brief Hardware polling function for write requests
     * @param hw_available_space Number of write requests hardware can accept
     * @return Number of write requests sent to hardware
     */
    int pollWriteRequests(int hw_available_space);
    
    /**
     * @brief Hardware polling function for read requests
     * @param hw_available_space Number of read requests hardware can accept
     * @return Number of read requests sent to hardware
     */
    int pollReadRequests(int hw_available_space);

private:
    std::string scope_name_;
    int max_batch_size_;
    
    // Software FIFO clients
    std::unique_ptr<WriteClient> write_client_;
    std::unique_ptr<ReadClient> read_client_;
    
    // DPI-C interface functions
    void setupDpiInterface();
    
    // Callback functions for sending requests to hardware
    int sendWriteRequests(const std::vector<WriteRequest>& requests, int max_count);
    int sendReadRequests(const std::vector<ReadRequest>& requests, int max_count);
};

/**
 * @brief Global manager for multiple SFIFO-HFIFO instances
 * 
 * This singleton manages multiple SFIFO-HFIFO instances, typically one per
 * SystemVerilog module instance.
 */
class SfifoHfifoManager {
public:
    /**
     * @brief Get singleton instance
     * @return Reference to the singleton instance
     */
    static SfifoHfifoManager& getInstance();
    
    /**
     * @brief Register a new SFIFO-HFIFO instance
     * @param instance_id Unique identifier for the instance
     * @param scope_name SystemVerilog scope name
     * @param max_batch_size Maximum batch size for transfers
     * @return Pointer to the created wrapper instance
     */
    SfifoHfifoWrapper* registerInstance(uint16_t instance_id, 
                                       const std::string& scope_name,
                                       int max_batch_size = 4);
    
    /**
     * @brief Get an existing instance
     * @param instance_id Instance identifier
     * @return Pointer to the wrapper instance, or nullptr if not found
     */
    SfifoHfifoWrapper* getInstance(uint16_t instance_id);
    
    /**
     * @brief Get instance by SystemVerilog scope
     * @param scope SystemVerilog scope
     * @return Pointer to the wrapper instance, or nullptr if not found
     */
    SfifoHfifoWrapper* getInstanceByScope(svScope scope);
    
    /**
     * @brief Unregister an instance
     * @param instance_id Instance identifier
     */
    void unregisterInstance(uint16_t instance_id);
    
    /**
     * @brief Clear all instances
     */
    void clearAllInstances();

private:
    SfifoHfifoManager() = default;
    ~SfifoHfifoManager() = default;
    
    // Prevent copying
    SfifoHfifoManager(const SfifoHfifoManager&) = delete;
    SfifoHfifoManager& operator=(const SfifoHfifoManager&) = delete;
    
    std::unordered_map<uint16_t, std::unique_ptr<SfifoHfifoWrapper>> instances_;
    std::unordered_map<svScope, uint16_t> scope_to_id_map_;
};

// Global DPI-C functions that can be called from SystemVerilog
extern "C" {
    /**
     * @brief Initialize a new SFIFO-HFIFO instance
     * @param instance_id Unique identifier for the instance
     * @param max_batch_size Maximum batch size for transfers
     */
    void sfifo_hfifo_init(uint16_t instance_id, int max_batch_size);
    
    /**
     * @brief Hardware polling function for write requests
     * @param instance_id Instance identifier
     * @param hw_available_space Number of write requests hardware can accept
     * @return Number of write requests sent to hardware
     */
    int sfifo_hfifo_poll_write(uint16_t instance_id, int hw_available_space);
    
    /**
     * @brief Hardware polling function for read requests
     * @param instance_id Instance identifier
     * @param hw_available_space Number of read requests hardware can accept
     * @return Number of read requests sent to hardware
     */
    int sfifo_hfifo_poll_read(uint16_t instance_id, int hw_available_space);
    
    /**
     * @brief Add a write request (called from software/testbench)
     * @param instance_id Instance identifier
     * @param addr Address to write
     * @param data Data to write (array of 32-bit words)
     * @param data_words Number of 32-bit words in data
     * @param mask Write mask (array of 32-bit words)
     * @param mask_words Number of 32-bit words in mask
     */
    void sfifo_hfifo_add_write(uint16_t instance_id, uint64_t addr,
                              const uint32_t* data, int data_words,
                              const uint32_t* mask, int mask_words);
    
    /**
     * @brief Add a read request (called from software/testbench)
     * @param instance_id Instance identifier
     * @param addr Address to read
     */
    void sfifo_hfifo_add_read(uint16_t instance_id, uint64_t addr);
    
    /**
     * @brief Get write FIFO size
     * @param instance_id Instance identifier
     * @return Number of pending write requests
     */
    int sfifo_hfifo_get_write_size(uint16_t instance_id);
    
    /**
     * @brief Get read FIFO size
     * @param instance_id Instance identifier
     * @return Number of pending read requests
     */
    int sfifo_hfifo_get_read_size(uint16_t instance_id);
}

#endif // SFIFO_HFIFO_WRAPPER_H
