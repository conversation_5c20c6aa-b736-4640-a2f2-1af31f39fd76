#include "sfifo_hfifo_wrapper.h"
#include <algorithm>

// Forward declarations for DPI-C functions exported from SystemVerilog
extern "C" {
    extern int hw_push_write_request(uint64_t addr, const uint32_t* data,
                                    const uint32_t* mask, int data_words);
    extern int hw_push_read_request(uint64_t addr);
    extern int hw_push_write_batch(const uint64_t* addrs, const uint32_t* data_array,
                                  const uint32_t* mask_array, int batch_size, int data_words_per_req);
    extern int hw_push_read_batch(const uint64_t* addrs, int batch_size);
}

// Dummy implementations for functions that will be exported from SystemVerilog
// These will be replaced by actual SystemVerilog functions at link time
int hw_push_write_request(uint64_t addr, const uint32_t* data,
                         const uint32_t* mask, int data_words) {
    vpi_printf("[SFIFO-HFIFO] hw_push_write_request called: addr=0x%lx\n", addr);
    return 1; // Success
}

int hw_push_read_request(uint64_t addr) {
    vpi_printf("[SFIFO-HFIFO] hw_push_read_request called: addr=0x%lx\n", addr);
    return 1; // Success
}

int hw_push_write_batch(const uint64_t* addrs, const uint32_t* data_array,
                       const uint32_t* mask_array, int batch_size, int data_words_per_req) {
    vpi_printf("[SFIFO-HFIFO] hw_push_write_batch called: batch_size=%d\n", batch_size);
    return batch_size; // All requests accepted
}

int hw_push_read_batch(const uint64_t* addrs, int batch_size) {
    vpi_printf("[SFIFO-HFIFO] hw_push_read_batch called: batch_size=%d\n", batch_size);
    return batch_size; // All requests accepted
}

// SfifoHfifoWrapper Implementation
SfifoHfifoWrapper::SfifoHfifoWrapper(const std::string& scope_name, int max_batch_size)
    : scope_name_(scope_name), max_batch_size_(max_batch_size) {

    // Create write client with callback
    auto write_callback = [this](const std::vector<WriteRequest>& requests, int max_count) -> int {
        return this->sendWriteRequests(requests, max_count);
    };
    write_client_ = std::make_unique<WriteClient>(write_callback, max_batch_size);

    // Create read client with callback
    auto read_callback = [this](const std::vector<ReadRequest>& requests, int max_count) -> int {
        return this->sendReadRequests(requests, max_count);
    };
    read_client_ = std::make_unique<ReadClient>(read_callback, max_batch_size);

    setupDpiInterface();
}

SfifoHfifoWrapper::~SfifoHfifoWrapper() {
    // Cleanup if needed
}

void SfifoHfifoWrapper::addWriteRequest(uint64_t addr, const std::vector<uint32_t>& data,
                                       const std::vector<uint32_t>& mask) {
    WriteRequest req(addr, data, mask);
    write_client_->pushRequest(req);
}

void SfifoHfifoWrapper::addReadRequest(uint64_t addr) {
    ReadRequest req(addr);
    read_client_->pushRequest(req);
}

size_t SfifoHfifoWrapper::getWriteFifoSize() const {
    return write_client_->size();
}

size_t SfifoHfifoWrapper::getReadFifoSize() const {
    return read_client_->size();
}

void SfifoHfifoWrapper::clearAllRequests() {
    write_client_->clear();
    read_client_->clear();
}

int SfifoHfifoWrapper::pollWriteRequests(int hw_available_space) {
    return write_client_->pollRequests(hw_available_space);
}

int SfifoHfifoWrapper::pollReadRequests(int hw_available_space) {
    return read_client_->pollRequests(hw_available_space);
}

void SfifoHfifoWrapper::setupDpiInterface() {
    // Set up SystemVerilog scope for DPI-C calls
    svScope scope = svGetScopeFromName(scope_name_.c_str());
    if (scope) {
        svSetScope(scope);
    } else {
        vpi_printf("Warning: Could not find scope %s\n", scope_name_.c_str());
    }
}

int SfifoHfifoWrapper::sendWriteRequests(const std::vector<WriteRequest>& requests, int max_count) {
    if (requests.empty()) {
        return 0;
    }

    // Set the correct scope for DPI-C calls
    svScope scope = svGetScopeFromName(scope_name_.c_str());
    if (scope) {
        svSetScope(scope);
    }

    int sent_count = 0;

    if (requests.size() == 1) {
        // Single request - use simple interface
        const WriteRequest& req = requests[0];
        if (hw_push_write_request(req.addr, req.data.data(), req.mask.data(),
                                 static_cast<int>(req.data.size()))) {
            sent_count = 1;
        }
    } else {
        // Batch requests - use batch interface for better performance
        int batch_size = std::min(static_cast<int>(requests.size()), max_count);

        // Prepare batch arrays
        std::vector<uint64_t> addrs(batch_size);
        std::vector<uint32_t> data_array;
        std::vector<uint32_t> mask_array;

        int data_words_per_req = 0;
        if (!requests.empty()) {
            data_words_per_req = static_cast<int>(requests[0].data.size());
        }

        data_array.reserve(batch_size * data_words_per_req);
        mask_array.reserve(batch_size * data_words_per_req);

        for (int i = 0; i < batch_size; ++i) {
            addrs[i] = requests[i].addr;
            data_array.insert(data_array.end(), requests[i].data.begin(), requests[i].data.end());
            mask_array.insert(mask_array.end(), requests[i].mask.begin(), requests[i].mask.end());
        }

        sent_count = hw_push_write_batch(addrs.data(), data_array.data(),
                                        mask_array.data(), batch_size, data_words_per_req);
    }

    return sent_count;
}

int SfifoHfifoWrapper::sendReadRequests(const std::vector<ReadRequest>& requests, int max_count) {
    if (requests.empty()) {
        return 0;
    }

    // Set the correct scope for DPI-C calls
    svScope scope = svGetScopeFromName(scope_name_.c_str());
    if (scope) {
        svSetScope(scope);
    }

    int sent_count = 0;

    if (requests.size() == 1) {
        // Single request - use simple interface
        if (hw_push_read_request(requests[0].addr)) {
            sent_count = 1;
        }
    } else {
        // Batch requests - use batch interface for better performance
        int batch_size = std::min(static_cast<int>(requests.size()), max_count);

        std::vector<uint64_t> addrs(batch_size);
        for (int i = 0; i < batch_size; ++i) {
            addrs[i] = requests[i].addr;
        }

        sent_count = hw_push_read_batch(addrs.data(), batch_size);
    }

    return sent_count;
}

// SfifoHfifoManager Implementation
SfifoHfifoManager& SfifoHfifoManager::getInstance() {
    static SfifoHfifoManager instance;
    return instance;
}

SfifoHfifoWrapper* SfifoHfifoManager::registerInstance(uint16_t instance_id,
                                                      const std::string& scope_name,
                                                      int max_batch_size) {
    auto wrapper = std::make_unique<SfifoHfifoWrapper>(scope_name, max_batch_size);
    SfifoHfifoWrapper* wrapper_ptr = wrapper.get();

    instances_[instance_id] = std::move(wrapper);

    // Map scope to instance ID
    svScope scope = svGetScopeFromName(scope_name.c_str());
    if (scope) {
        scope_to_id_map_[scope] = instance_id;
    }

    return wrapper_ptr;
}

SfifoHfifoWrapper* SfifoHfifoManager::getInstance(uint16_t instance_id) {
    auto it = instances_.find(instance_id);
    return (it != instances_.end()) ? it->second.get() : nullptr;
}

SfifoHfifoWrapper* SfifoHfifoManager::getInstanceByScope(svScope scope) {
    auto it = scope_to_id_map_.find(scope);
    if (it != scope_to_id_map_.end()) {
        return getInstance(it->second);
    }
    return nullptr;
}

void SfifoHfifoManager::unregisterInstance(uint16_t instance_id) {
    auto it = instances_.find(instance_id);
    if (it != instances_.end()) {
        // Remove from scope map
        for (auto scope_it = scope_to_id_map_.begin(); scope_it != scope_to_id_map_.end(); ++scope_it) {
            if (scope_it->second == instance_id) {
                scope_to_id_map_.erase(scope_it);
                break;
            }
        }
        instances_.erase(it);
    }
}

void SfifoHfifoManager::clearAllInstances() {
    instances_.clear();
    scope_to_id_map_.clear();
}

// Global DPI-C functions implementation
extern "C" {
    void sfifo_hfifo_init(uint16_t instance_id, int max_batch_size) {
        svScope scope = svGetScope();
        if (!scope) {
            vpi_printf("Error: Could not get scope in sfifo_hfifo_init\n");
            return;
        }

        std::string scope_name = svGetNameFromScope(scope);
        SfifoHfifoManager::getInstance().registerInstance(instance_id, scope_name, max_batch_size);

        vpi_printf("[SFIFO-HFIFO] Initialized instance %d with scope %s, max_batch_size=%d\n",
                   instance_id, scope_name.c_str(), max_batch_size);
    }

    int sfifo_hfifo_poll_write(uint16_t instance_id, int hw_available_space) {
        SfifoHfifoWrapper* wrapper = SfifoHfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return 0;
        }
        return wrapper->pollWriteRequests(hw_available_space);
    }

    int sfifo_hfifo_poll_read(uint16_t instance_id, int hw_available_space) {
        SfifoHfifoWrapper* wrapper = SfifoHfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return 0;
        }
        return wrapper->pollReadRequests(hw_available_space);
    }

    void sfifo_hfifo_add_write(uint16_t instance_id, uint64_t addr,
                              const uint32_t* data, int data_words,
                              const uint32_t* mask, int mask_words) {
        SfifoHfifoWrapper* wrapper = SfifoHfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return;
        }

        std::vector<uint32_t> data_vec(data, data + data_words);
        std::vector<uint32_t> mask_vec(mask, mask + mask_words);
        wrapper->addWriteRequest(addr, data_vec, mask_vec);
    }

    void sfifo_hfifo_add_read(uint16_t instance_id, uint64_t addr) {
        SfifoHfifoWrapper* wrapper = SfifoHfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return;
        }
        wrapper->addReadRequest(addr);
    }

    int sfifo_hfifo_get_write_size(uint16_t instance_id) {
        SfifoHfifoWrapper* wrapper = SfifoHfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return 0;
        }
        return static_cast<int>(wrapper->getWriteFifoSize());
    }

    int sfifo_hfifo_get_read_size(uint16_t instance_id) {
        SfifoHfifoWrapper* wrapper = SfifoHfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return 0;
        }
        return static_cast<int>(wrapper->getReadFifoSize());
    }
}
