`timescale 1ns/1ps


module hbm3_pc_bfm_tb;

    // Parameters
    localparam ADDR_WIDTH = 10;
    localparam DATA_WIDTH = 256;
    localparam DATA_BYTE_WIDTH = DATA_WIDTH/8;
    localparam PAGE_SIZE = 256*64;
    localparam [9:0] HBM_ID = 1;
    localparam [4:0] CHANNEL_ID = 7;
    localparam [0:0] PC_ID = 1;

    localparam BFM_NUM = 2;

    // Clock and reset
    reg clk;
    reg rstn;

    // Write interface signals
    reg                         mm_wen [0: BFM_NUM-1];
    reg [64-1:0]                mm_waddr [0:BFM_NUM-1];
    reg [DATA_WIDTH-1:0]       mm_wdata [0:BFM_NUM-1];
    reg [DATA_BYTE_WIDTH-1:0]  mm_wmask [0:BFM_NUM-1];

    // Read interface signals
    reg                         mm_ren [0:BFM_NUM-1];
    reg [64-1:0]               mm_raddr [0:BFM_NUM-1];
    wire [DATA_WIDTH-1:0]      mm_rdata [0:BFM_NUM-1];
    wire                       mm_rvalid [0:BFM_NUM-1];

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;
    end

    // DUT instantiation
    genvar inst_num;
    generate
        for (inst_num = 0; inst_num < BFM_NUM; inst_num = inst_num + 1) begin :dut
            hbm3_pc_bfm #(
                .HBM_ID(HBM_ID),
                .CHANNEL_ID(CHANNEL_ID+inst_num),
                .PC_ID(PC_ID),
                .ADDR_WIDTH(ADDR_WIDTH),
                .DATA_WIDTH(DATA_WIDTH),
                .PAGE_SIZE(PAGE_SIZE),
                .BASE_ADDR(64'h0000_0000_8000_0000),
                .CHANNEL_NUM(16)
            ) u_hbm3_pc_bfm (
                .mm_clk(clk),
                .mm_rstn(rstn),
                // Write port
                .mm_wen(mm_wen[inst_num]),
                .mm_waddr(mm_waddr[inst_num]),
                .mm_wdata(mm_wdata[inst_num]),
                .mm_wmask(mm_wmask[inst_num]),
                // Read port
                .mm_ren(mm_ren[inst_num]),
                .mm_raddr(mm_raddr[inst_num]),
                .mm_rdata(mm_rdata[inst_num]),
                .mm_rvalid(mm_rvalid[inst_num])
            );
        end
    endgenerate

    bit [15:0] id = {PC_ID, CHANNEL_ID, HBM_ID};

    bit [DATA_WIDTH-1:0] wdata [0:BFM_NUM-1];
    bit [DATA_WIDTH-1:0] rdata [0:BFM_NUM-1];
    bit [DATA_BYTE_WIDTH-1:0] wmask={DATA_BYTE_WIDTH{1'b1}};
    bit [63:0] addr [0:BFM_NUM-1];

    event initmem[0:BFM_NUM-1];
    event resetev[0:BFM_NUM-1];
    event done_ent [0:BFM_NUM-1];

    generate
        for(genvar i = 0; i < BFM_NUM; i++) begin :meminit
            initial begin
                $readmemh("mem.hex", dut[i].u_hbm3_pc_bfm.memory);
                $readmemh("page.hex", dut[i].u_hbm3_pc_bfm.page_table);
                $display("[SV] Memory and page table initialized %d", i);
            end
        end
    endgenerate

    initial begin
        #0;
        $display("[SV] Memory and page table initialized 0");

        //#100;

        for (int i = 0; i < BFM_NUM; i++) begin :ioinit
            mm_wen[i] <= 0;
            mm_waddr[i] <= 0;
            mm_wdata[i] <= 0;
            mm_wmask[i] <= 0;
            mm_ren[i] <= 0;
            mm_raddr[i] <= 0;
        end

        $display("[SV] Reset sequence");

        // Reset sequence
        rstn <= 0;
        #100;
        rstn <= 1;

        #100;

        for (int i = 0; i < BFM_NUM; i++) begin
            -> resetev[i];
        end
    end

    // make sure the address is different for different PC instances. or the fakeC doens't work
    generate
        for(genvar i = 0; i < BFM_NUM; i++) begin :test
            initial begin

                @(resetev[i]);

                //#(i*100);

                // Test 1
                wdata[i] <= 256'h0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0a_0a0_a0a0_a0a0_a0a0_a0a0_a0a0 + i;
                addr[i] <= 64'h80000120 + i*1024*DATA_WIDTH/8;

                @(posedge clk);

                dut[i].u_hbm3_pc_bfm.start_write_test(addr[i], wdata[i], wmask);
                #10;
                dut[i].u_hbm3_pc_bfm.start_read_test(addr[i]);
                repeat(10) @(posedge clk);
                rdata[i] = dut[i].u_hbm3_pc_bfm.start_read_back(addr[i]);

                if (rdata[i] === wdata[i]) begin
                    $display("[SV] inst %0d Test 1 passed", i);
                end else begin
                    $display("[SV] inst %0d Test 1 **failed** rdata=%x wdata=%x", i, rdata[i], wdata[i]);
                end


                // Test 2

                wdata[i] <= 256'h5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_5a5a_a5a5 + i;
                addr[i] <= 64'h80000060 + i*1024*DATA_WIDTH/8;

                @(posedge clk);

                dut[i].u_hbm3_pc_bfm.start_write_test(addr[i], wdata[i], wmask);
                #10;
                dut[i].u_hbm3_pc_bfm.start_read_test(addr[i]);
                repeat(10) @(posedge clk);
                rdata[i] = dut[i].u_hbm3_pc_bfm.start_read_back(addr[i]);

                if (rdata[i] === wdata[i]) begin
                    $display("[SV] inst %0d Test 2 passed", i);
                end else begin
                    $display("[SV] inst %0d Test 2 **failed** rdata=%x wdata=%x", i, rdata[i], wdata[i]);
                end

                // Test 3
                wdata[i] <= 256'h55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa + i;
                addr[i] <= 64'h80000120 + i*1024*DATA_WIDTH/8;

                @(posedge clk);

                dut[i].u_hbm3_pc_bfm.start_write_test(addr[i], wdata[i], wmask);
                #10;
                dut[i].u_hbm3_pc_bfm.start_read_test(addr[i]);
                repeat(10) @(posedge clk);
                rdata[i] = dut[i].u_hbm3_pc_bfm.start_read_back(addr[i]);

                if (rdata[i] === wdata[i]) begin
                    $display("[SV] inst %0d Test 3 passed", i);
                end else begin
                    $display("[SV] inst %0d Test 3 **failed** rdata=%x wdata=%x", i, rdata[i], wdata[i]);
                end

                @(posedge clk);

                // Test 4
                for (int j = 0; j < 16; j++) begin
                    wdata[i] <= 256'hdead_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_0000 + i +j;
                    addr[i] <= 64'h80000000 + i*1024*DATA_WIDTH/8 + j*32;
                    repeat(1) @(posedge clk);
                    dut[i].u_hbm3_pc_bfm.start_write_test(addr[i], wdata[i], wmask);
                end

                @(posedge clk);

                for (int j = 0; j < 16; j++) begin
                    addr[i] <= 64'h80000000 + i*1024*DATA_WIDTH/8 + j*32;
                    wdata[i] <= 256'hdead_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_55aa_0000 + i +j;
                    repeat(1) @(posedge clk);
                    dut[i].u_hbm3_pc_bfm.start_read_test(addr[i]);
                    repeat(3) @(posedge clk);
                    rdata[i] = dut[i].u_hbm3_pc_bfm.start_read_back(addr[i]);
                    
                    if (rdata[i] === wdata[i]) begin
                        $display("[SV] inst %0d Test 4-%0d passed", i, j);
                    end else begin
                        $display("[SV] inst %0d Test 4-%0d **failed** rdata=%x wdata=%x", i, j, rdata[i], wdata[i]);
                    end
                end
                -> done_ent[i];
            end
        end
    endgenerate


    initial begin
        fork 
            begin
                @(done_ent[0]);
            end
            begin
                @(done_ent[1]);
            end
        join
        $display("[SV] Test done");
        $finish;
    end



endmodule 
