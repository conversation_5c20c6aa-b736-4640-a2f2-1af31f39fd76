# Get all subdirectories excluding hidden ones
SUBDIRS := $(shell find . -maxdepth 1 -type d ! -name ".*" ! -name ".")

.PHONY: runvcs runxlm clean cleanall $(SUBDIRS)

# Target to run vcs compilation and simulation
runvcs: $(SUBDIRS)
	@for dir in $(SUBDIRS); do \
		echo "Running vcs in $$dir"; \
		cd $$dir && make vcs_comp vcs_run && cd ..; \
	done

# Target to run xlm compilation and simulation
runxlm: $(SUBDIRS)
	@for dir in $(SUBDIRS); do \
		echo "Running xlm in $$dir"; \
		cd $$dir && make xlm_comp xlm_run && cd ..; \
	done

# Target to clean all subdirectories
clean: $(SUBDIRS)
	@for dir in $(SUBDIRS); do \
		echo "Cleaning $$dir"; \
		cd $$dir && make clean && cd ..; \
	done

# Target to cleanall all subdirectories
cleanall: $(SUBDIRS)
	@for dir in $(SUBDIRS); do \
		echo "Cleaning all in $$dir"; \
		cd $$dir && make cleanall && cd ..; \
	done 