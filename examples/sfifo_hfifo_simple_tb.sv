/**
 * @file sfifo_hfifo_simple_tb.sv
 * @brief Simplified SystemVerilog testbench for SFIFO-HFIFO demonstration
 *
 * This testbench demonstrates the SFIFO-HFIFO concept with DPI-C integration
 * using a simplified approach that focuses on the core functionality.
 */

`timescale 1ns/1ps

module sfifo_hfifo_simple_tb;

    // Clock and reset
    reg clk;
    reg rstn;

    // Test parameters
    parameter INSTANCE_ID = 0;
    parameter MAX_BATCH_SIZE = 4;
    parameter POLLING_INTERVAL = 100;  // Clock cycles

    // Polling counters
    reg [31:0] write_poll_counter;
    reg [31:0] read_poll_counter;

    // Statistics
    int total_write_requests = 0;
    int total_read_requests = 0;
    int write_polls = 0;
    int read_polls = 0;

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;  // 100MHz clock
    end

    // Reset sequence
    initial begin
        rstn = 0;
        write_poll_counter = 0;
        read_poll_counter = 0;

        #100;
        rstn = 1;

        $display("[TB] Reset completed at time %t", $time);

        // Initialize SFIFO-HFIFO
        sfifo_hfifo_init(INSTANCE_ID, MAX_BATCH_SIZE);

        #200;

        // Test connectivity
        sfifo_hfifo_test_connectivity();

        #100;

        // Start the test
        $display("[TB] Starting SFIFO-HFIFO test at time %t", $time);
        sfifo_hfifo_start_test();
    end

    // Polling logic for write requests
    always @(posedge clk) begin
        if (rstn) begin
            write_poll_counter <= write_poll_counter + 1;

            if (write_poll_counter >= POLLING_INTERVAL) begin
                automatic int hw_space = 8;  // Simulate 8 slots available
                automatic int requests_received;

                write_poll_counter <= 0;
                requests_received = sfifo_hfifo_poll_write(INSTANCE_ID, hw_space);

                if (requests_received > 0) begin
                    total_write_requests <= total_write_requests + requests_received;
                    write_polls <= write_polls + 1;
                    $display("[TB] Write poll %d: received %d requests (total: %d) @ %t",
                            write_polls + 1, requests_received, total_write_requests + requests_received, $time);
                end
            end
        end
    end

    // Polling logic for read requests
    always @(posedge clk) begin
        if (rstn) begin
            read_poll_counter <= read_poll_counter + 1;

            if (read_poll_counter >= POLLING_INTERVAL + 25) begin  // Slightly offset
                automatic int hw_space = 6;  // Simulate 6 slots available
                automatic int requests_received;

                read_poll_counter <= 0;
                requests_received = sfifo_hfifo_poll_read(INSTANCE_ID, hw_space);

                if (requests_received > 0) begin
                    total_read_requests <= total_read_requests + requests_received;
                    read_polls <= read_polls + 1;
                    $display("[TB] Read poll %d: received %d requests (total: %d) @ %t",
                            read_polls + 1, requests_received, total_read_requests + requests_received, $time);
                end
            end
        end
    end

    // Status monitoring
    reg [31:0] status_counter = 0;
    always @(posedge clk) begin
        if (rstn) begin
            status_counter <= status_counter + 1;

            if (status_counter >= 10000) begin  // Every 100us
                automatic int write_fifo_size, read_fifo_size;

                status_counter <= 0;
                sfifo_hfifo_get_status(INSTANCE_ID, write_fifo_size, read_fifo_size);

                if (write_fifo_size > 0 || read_fifo_size > 0) begin
                    $display("[TB] Status @ %t: Write FIFO=%d, Read FIFO=%d",
                            $time, write_fifo_size, read_fifo_size);
                end
            end
        end
    end

    // Test completion detection
    reg test_completed = 0;
    reg [31:0] completion_check_counter = 0;

    always @(posedge clk) begin
        if (rstn && !test_completed) begin
            completion_check_counter <= completion_check_counter + 1;

            if (completion_check_counter >= 50000) begin  // Check every 500us
                automatic int write_fifo_size, read_fifo_size;

                completion_check_counter <= 0;
                sfifo_hfifo_get_status(INSTANCE_ID, write_fifo_size, read_fifo_size);

                // Check if test is complete (all FIFOs empty and some requests processed)
                if (write_fifo_size == 0 && read_fifo_size == 0 &&
                    total_write_requests > 0 && total_read_requests > 0) begin

                    test_completed <= 1;

                    $display("[TB] ========================================");
                    $display("[TB] TEST COMPLETED SUCCESSFULLY!");
                    $display("[TB] ========================================");
                    $display("[TB] Final Statistics:");
                    $display("[TB]   Total Write Requests: %d", total_write_requests);
                    $display("[TB]   Total Read Requests:  %d", total_read_requests);
                    $display("[TB]   Write Polls:          %d", write_polls);
                    $display("[TB]   Read Polls:           %d", read_polls);
                    $display("[TB]   Avg Writes per Poll:  %.2f",
                            write_polls > 0 ? real'(total_write_requests) / real'(write_polls) : 0.0);
                    $display("[TB]   Avg Reads per Poll:   %.2f",
                            read_polls > 0 ? real'(total_read_requests) / real'(read_polls) : 0.0);
                    $display("[TB] ========================================");

                    // Cleanup and finish
                    #1000;
                    sfifo_hfifo_cleanup();
                    #100;
                    $finish;
                end
            end
        end
    end

    // Timeout protection
    initial begin
        #500000;  // 5ms timeout
        if (!test_completed) begin
            $display("[TB] ERROR: Test timeout! Simulation did not complete in time.");
            $display("[TB] Current status:");
            $display("[TB]   Write requests processed: %d", total_write_requests);
            $display("[TB]   Read requests processed:  %d", total_read_requests);

            sfifo_hfifo_cleanup();
            $finish;
        end
    end

    // DPI-C function imports
    import "DPI-C" context function void sfifo_hfifo_init(
        input shortint unsigned instance_id,
        input int max_batch_size
    );

    import "DPI-C" context function int sfifo_hfifo_poll_write(
        input shortint unsigned instance_id,
        input int hw_available_space
    );

    import "DPI-C" context function int sfifo_hfifo_poll_read(
        input shortint unsigned instance_id,
        input int hw_available_space
    );

    import "DPI-C" context function void sfifo_hfifo_get_status(
        input shortint unsigned instance_id,
        output int write_size,
        output int read_size
    );

    import "DPI-C" context function void sfifo_hfifo_start_test();
    import "DPI-C" context function void sfifo_hfifo_test_connectivity();
    import "DPI-C" context function void sfifo_hfifo_cleanup();

    // Optional: VCD dump for debugging
    `ifdef DUMP_VCD
    initial begin
        $dumpfile("sfifo_hfifo_simple.vcd");
        $dumpvars(0, sfifo_hfifo_simple_tb);
        $display("[TB] VCD dumping enabled: sfifo_hfifo_simple.vcd");
    end
    `endif

endmodule

/**
 * This testbench demonstrates:
 *
 * 1. **Hardware Polling**: Regular polling of software FIFOs
 * 2. **Batch Processing**: Multiple requests per polling cycle
 * 3. **Performance Monitoring**: Statistics on batch efficiency
 * 4. **DPI-C Integration**: Clean interface between SW and HW
 * 5. **Automatic Testing**: Self-contained test with completion detection
 *
 * Key Benefits Shown:
 * - Original: 1 request per poll
 * - Optimized: Up to MAX_BATCH_SIZE requests per poll
 * - Performance improvement: Measured by avg requests per poll
 * - Reduced polling overhead: Fewer DPI-C calls for same work
 *
 * Expected Results:
 * - Write requests: 8 total, processed in ~2-3 polls (batch efficiency)
 * - Read requests: 8 total, processed in ~2-3 polls (batch efficiency)
 * - Avg requests per poll: 2-4 (showing batch optimization)
 *
 * Usage:
 *   make libsfifo_hfifo.so
 *   make vcs_comp
 *   make vcs_run
 */
