/**
 * @file sfifo_hfifo_dpi_tb.sv
 * @brief SystemVerilog testbench for SFIFO-HFIFO DPI-C integration
 * 
 * This testbench demonstrates the complete SFIFO-HFIFO system with
 * DPI-C integration, showing how software and hardware sides work together.
 */

`timescale 1ns/1ps

module sfifo_hfifo_dpi_tb;
    
    // Clock and reset
    reg clk;
    reg rstn;
    
    // Test parameters
    parameter INSTANCE_ID = 0;
    parameter FIFO_DEPTH = 16;
    parameter ADDR_WIDTH = 64;
    parameter DATA_WIDTH = 256;
    parameter DATA_WIDTH_IN_BYTE = DATA_WIDTH/8;
    parameter MAX_BATCH_SIZE = 4;
    
    // SFIFO-HFIFO interface signals
    wire write_req_valid;
    wire [ADDR_WIDTH-1:0] write_req_addr;
    wire [DATA_WIDTH-1:0] write_req_data;
    wire [DATA_WIDTH_IN_BYTE-1:0] write_req_mask;
    reg write_req_ready;
    
    wire read_req_valid;
    wire [ADDR_WIDTH-1:0] read_req_addr;
    reg read_req_ready;
    
    wire write_fifo_empty, write_fifo_full;
    wire read_fifo_empty, read_fifo_full;
    
    // Simple memory for demonstration
    reg [DATA_WIDTH-1:0] memory [0:1023];
    
    // Statistics
    int write_count = 0;
    int read_count = 0;
    
    // Instantiate the integrated SFIFO-HFIFO module
    sfifo_hfifo_integrated #(
        .INSTANCE_ID(INSTANCE_ID),
        .FIFO_DEPTH(FIFO_DEPTH),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .DATA_WIDTH_IN_BYTE(DATA_WIDTH_IN_BYTE),
        .POLLING_INTERVAL(50),  // Fast polling for testing
        .MAX_BATCH_SIZE(MAX_BATCH_SIZE)
    ) sfifo_hfifo_inst (
        .clk(clk),
        .rstn(rstn),
        
        // Write interface
        .write_req_valid(write_req_valid),
        .write_req_addr(write_req_addr),
        .write_req_data(write_req_data),
        .write_req_mask(write_req_mask),
        .write_req_ready(write_req_ready),
        
        // Read interface
        .read_req_valid(read_req_valid),
        .read_req_addr(read_req_addr),
        .read_req_ready(read_req_ready),
        
        // Status
        .write_fifo_empty(write_fifo_empty),
        .write_fifo_full(write_fifo_full),
        .read_fifo_empty(read_fifo_empty),
        .read_fifo_full(read_fifo_full)
    );
    
    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;  // 100MHz clock
    end
    
    // Reset sequence
    initial begin
        rstn = 0;
        write_req_ready = 1;
        read_req_ready = 1;
        
        #100;
        rstn = 1;
        
        $display("[TB] Reset completed, starting SFIFO-HFIFO test");
    end
    
    // Handle write requests
    always @(posedge clk) begin
        if (rstn && write_req_valid && write_req_ready) begin
            // Apply mask and write to memory
            for (int i = 0; i < DATA_WIDTH_IN_BYTE; i++) begin
                if (write_req_mask[i]) begin
                    memory[write_req_addr[9:0]][i*8 +: 8] <= write_req_data[i*8 +: 8];
                end
            end
            
            write_count <= write_count + 1;
            $display("[TB] Write processed: addr=0x%x, data=0x%x, count=%d", 
                    write_req_addr, write_req_data, write_count + 1);
        end
    end
    
    // Handle read requests
    always @(posedge clk) begin
        if (rstn && read_req_valid && read_req_ready) begin
            // Read from memory (in real design, you'd send response back)
            reg [DATA_WIDTH-1:0] read_data;
            read_data = memory[read_req_addr[9:0]];
            
            read_count <= read_count + 1;
            $display("[TB] Read processed: addr=0x%x, data=0x%x, count=%d", 
                    read_req_addr, read_data, read_count + 1);
        end
    end
    
    // Status monitoring
    always @(posedge clk) begin
        if (rstn) begin
            // Monitor FIFO status changes
            if (!write_fifo_empty || !read_fifo_empty) begin
                if ($time % 1000 == 0) begin  // Print every 1000ns
                    $display("[TB] FIFO Status @ %t: Write=%s, Read=%s", 
                            $time,
                            write_fifo_empty ? "Empty" : "Not Empty",
                            read_fifo_empty ? "Empty" : "Not Empty");
                end
            end
        end
    end
    
    // Test sequence
    initial begin
        // Wait for reset
        wait(rstn);
        #200;
        
        $display("[TB] Starting DPI-C connectivity test");
        modern_test_connectivity();
        
        #100;
        $display("[TB] Starting SFIFO-HFIFO test");
        modern_start_test();
        
        // Run simulation for a while
        #50000;
        
        $display("[TB] Test completed");
        $display("[TB] Final statistics: Writes=%d, Reads=%d", write_count, read_count);
        
        // Cleanup
        modern_cleanup();
        
        $finish;
    end
    
    // Timeout protection
    initial begin
        #100000;  // 100us timeout
        $display("[TB] ERROR: Simulation timeout!");
        $finish;
    end
    
    // DPI-C function imports
    import "DPI-C" context function void modern_test_connectivity();
    import "DPI-C" context function void modern_start_test();
    import "DPI-C" context function void modern_cleanup();
    
    // Optional: VCD dump for debugging
    `ifdef DUMP_VCD
    initial begin
        $dumpfile("sfifo_hfifo_test.vcd");
        $dumpvars(0, sfifo_hfifo_dpi_tb);
    end
    `endif

endmodule

/**
 * Usage Instructions:
 * 
 * 1. Build the shared library:
 *    make libsfifo_hfifo.so
 * 
 * 2. Compile with VCS:
 *    cd sim/vcs
 *    vcs -full64 -kdb -sverilog -debug_access+all \
 *        ../../rtl/sfifo_hfifo_hw.sv \
 *        ../../examples/sfifo_hfifo_dpi_tb.sv \
 *        -CFLAGS -g
 * 
 * 3. Run simulation:
 *    ./simv -sv_lib ../../libsfifo_hfifo -l sim.log
 * 
 * Or use the Makefile targets:
 *    make vcs_comp
 *    make vcs_run
 * 
 * Expected Output:
 * - DPI-C connectivity test passes
 * - SFIFO-HFIFO initialization succeeds
 * - Batch requests are generated and processed
 * - Performance optimization is demonstrated
 * - All FIFOs are eventually empty
 */
