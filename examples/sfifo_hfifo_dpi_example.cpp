/**
 * @file sfifo_hfifo_dpi_example.cpp
 * @brief DPI-C example for SFIFO-HFIFO integration with SystemVerilog
 * 
 * This file contains the C++ implementation that will be compiled into a .so
 * library and loaded by VCS/Xcelium for DPI-C integration.
 */

#include "sfifo_hfifo_wrapper.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>

// Global instance for demonstration
static SfifoHfifoWrapper* g_wrapper = nullptr;

// DPI-C exported functions that will be called from SystemVerilog
extern "C" {
    
    /**
     * @brief Initialize the SFIFO-HFIFO system
     * Called from SystemVerilog initial block
     */
    void modern_init_sfifo_hfifo(unsigned short instance_id, int max_batch_size) {
        svScope scope = svGetScope();
        if (!scope) {
            vpi_printf("Error: Could not get scope in modern_init_sfifo_hfifo\n");
            return;
        }
        
        std::string scope_name = svGetNameFromScope(scope);
        vpi_printf("[DPI] Initializing SFIFO-HFIFO with ID=%d, scope=%s, batch_size=%d\n", 
                   instance_id, scope_name.c_str(), max_batch_size);
        
        // Register with the manager
        g_wrapper = SfifoHfifoManager::getInstance().registerInstance(
            instance_id, scope_name, max_batch_size);
        
        if (g_wrapper) {
            vpi_printf("[DPI] SFIFO-HFIFO initialized successfully\n");
        } else {
            vpi_printf("[DPI] Error: Failed to initialize SFIFO-HFIFO\n");
        }
    }
    
    /**
     * @brief Add a write request from testbench
     */
    void modern_add_write_request(unsigned short instance_id, 
                                 unsigned long long addr,
                                 const unsigned int* data, int data_words,
                                 const unsigned int* mask, int mask_words) {
        if (!g_wrapper) {
            vpi_printf("[DPI] Error: SFIFO-HFIFO not initialized\n");
            return;
        }
        
        std::vector<uint32_t> data_vec(data, data + data_words);
        std::vector<uint32_t> mask_vec(mask, mask + mask_words);
        
        g_wrapper->addWriteRequest(addr, data_vec, mask_vec);
        
        vpi_printf("[DPI] Added write request: addr=0x%llx, data_words=%d, fifo_size=%zu\n", 
                   addr, data_words, g_wrapper->getWriteFifoSize());
    }
    
    /**
     * @brief Add a read request from testbench
     */
    void modern_add_read_request(unsigned short instance_id, unsigned long long addr) {
        if (!g_wrapper) {
            vpi_printf("[DPI] Error: SFIFO-HFIFO not initialized\n");
            return;
        }
        
        g_wrapper->addReadRequest(addr);
        
        vpi_printf("[DPI] Added read request: addr=0x%llx, fifo_size=%zu\n", 
                   addr, g_wrapper->getReadFifoSize());
    }
    
    /**
     * @brief Get FIFO status
     */
    void modern_get_fifo_status(unsigned short instance_id, 
                               int* write_size, int* read_size) {
        if (!g_wrapper) {
            *write_size = -1;
            *read_size = -1;
            return;
        }
        
        *write_size = static_cast<int>(g_wrapper->getWriteFifoSize());
        *read_size = static_cast<int>(g_wrapper->getReadFifoSize());
    }
    
    /**
     * @brief Start the modern test
     * This function demonstrates how to use the abstracted SFIFO-HFIFO
     */
    void modern_start_test() {
        vpi_printf("[DPI] Starting modern SFIFO-HFIFO test\n");
        
        if (!g_wrapper) {
            vpi_printf("[DPI] Error: SFIFO-HFIFO not initialized\n");
            return;
        }
        
        // Start a background thread to generate test requests
        std::thread test_thread([&]() {
            // Wait a bit for simulation to stabilize
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            vpi_printf("[DPI] Generating test requests...\n");
            
            // Generate write requests
            for (int i = 0; i < 10; i++) {
                uint64_t addr = 0x80000000ULL + i * 0x20;
                std::vector<uint32_t> data = {
                    0x12345600 + i, 0x90abcdef, 0x12345678, 0x90abcdef,
                    0x12345678, 0x90abcdef, 0x12345678, 0x90abcdef
                };
                std::vector<uint32_t> mask = {0xffffffff};
                
                g_wrapper->addWriteRequest(addr, data, mask);
                
                vpi_printf("[DPI] Generated write request %d: addr=0x%llx\n", i, addr);
                
                // Small delay between requests
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            
            // Generate read requests
            for (int i = 0; i < 10; i++) {
                uint64_t addr = 0x80000000ULL + i * 0x20;
                
                g_wrapper->addReadRequest(addr);
                
                vpi_printf("[DPI] Generated read request %d: addr=0x%llx\n", i, addr);
                
                // Small delay between requests
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            
            vpi_printf("[DPI] Test request generation completed\n");
            
            // Monitor FIFO status
            for (int i = 0; i < 100; i++) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                
                size_t write_size = g_wrapper->getWriteFifoSize();
                size_t read_size = g_wrapper->getReadFifoSize();
                
                if (write_size == 0 && read_size == 0) {
                    vpi_printf("[DPI] All requests processed, test completed successfully!\n");
                    break;
                }
                
                if (i % 10 == 0) {  // Print status every second
                    vpi_printf("[DPI] FIFO status: write=%zu, read=%zu\n", write_size, read_size);
                }
            }
        });
        
        test_thread.detach();
    }
    
    /**
     * @brief Cleanup function called at end of simulation
     */
    void modern_cleanup() {
        vpi_printf("[DPI] Cleaning up SFIFO-HFIFO\n");
        SfifoHfifoManager::getInstance().clearAllInstances();
        g_wrapper = nullptr;
    }
    
    /**
     * @brief Test function to verify DPI-C connectivity
     */
    void modern_test_connectivity() {
        vpi_printf("[DPI] SFIFO-HFIFO DPI-C connectivity test passed!\n");
        vpi_printf("[DPI] Key features available:\n");
        vpi_printf("[DPI]   ✓ Batch processing optimization\n");
        vpi_printf("[DPI]   ✓ Type-safe template design\n");
        vpi_printf("[DPI]   ✓ Configurable batch sizes\n");
        vpi_printf("[DPI]   ✓ Thread-safe operations\n");
        vpi_printf("[DPI]   ✓ Automatic polling management\n");
    }
}

// Dummy implementations for hardware-side functions
// These will be overridden by the actual SystemVerilog exports
extern "C" {
    int hw_push_write_request(uint64_t addr, const uint32_t* data, 
                             const uint32_t* mask, int data_words) {
        vpi_printf("[DPI] hw_push_write_request: addr=0x%llx, data_words=%d\n", addr, data_words);
        return 1; // Success
    }
    
    int hw_push_read_request(uint64_t addr) {
        vpi_printf("[DPI] hw_push_read_request: addr=0x%llx\n", addr);
        return 1; // Success
    }
    
    int hw_push_write_batch(const uint64_t* addrs, const uint32_t* data_array,
                           const uint32_t* mask_array, int batch_size, int data_words_per_req) {
        vpi_printf("[DPI] hw_push_write_batch: batch_size=%d, data_words_per_req=%d\n", 
                   batch_size, data_words_per_req);
        for (int i = 0; i < batch_size; i++) {
            vpi_printf("[DPI]   Request %d: addr=0x%llx\n", i, addrs[i]);
        }
        return batch_size; // All accepted
    }
    
    int hw_push_read_batch(const uint64_t* addrs, int batch_size) {
        vpi_printf("[DPI] hw_push_read_batch: batch_size=%d\n", batch_size);
        for (int i = 0; i < batch_size; i++) {
            vpi_printf("[DPI]   Request %d: addr=0x%llx\n", i, addrs[i]);
        }
        return batch_size; // All accepted
    }
}

/**
 * Usage Instructions:
 * 
 * 1. Compile this file into a shared library:
 *    make libsfifo_hfifo.so
 * 
 * 2. Compile SystemVerilog with VCS:
 *    make vcs_comp
 * 
 * 3. Run simulation with the shared library:
 *    make vcs_run
 * 
 * 4. For GUI mode:
 *    make vcs_grun
 * 
 * The simulation will demonstrate:
 * - Automatic SFIFO-HFIFO initialization
 * - Batch request processing
 * - Performance optimization
 * - Clean abstraction interface
 */
