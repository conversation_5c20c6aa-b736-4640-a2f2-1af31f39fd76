/**
 * @file sfifo_hfifo_usage_example.cpp
 * @brief Example showing how to use the abstracted SFIFO-HFIFO modules
 * 
 * This example demonstrates how to replace the existing manual FIFO management
 * in hbm3_pc_bfm_tb.cc with the new abstracted SFIFO-HFIFO classes.
 */

#include "sfifo_hfifo_wrapper.h"
#include <iostream>
#include <thread>
#include <chrono>

// Example: Replacing the old BFMContext with new abstracted approach
class ModernBFMContext {
public:
    ModernBFMContext(uint16_t bfm_id, svScope bfm_scope) 
        : id(bfm_id)
        , scope(bfm_scope)
        , scope_name(svGetNameFromScope(bfm_scope))
        , is_valid(true) {
        
        // Register with the SFIFO-HFIFO manager
        std::string scope_str = scope_name;
        sfifo_wrapper = SfifoHfifoManager::getInstance().registerInstance(
            bfm_id, scope_str, 4  // max_batch_size = 4
        );
    }
    
    // Add write request using the new abstracted interface
    void addWriteRequest(uint64_t addr, const std::vector<uint32_t>& data, 
                        const std::vector<uint32_t>& mask) {
        if (sfifo_wrapper) {
            sfifo_wrapper->addWriteRequest(addr, data, mask);
        }
    }
    
    // Add read request using the new abstracted interface
    void addReadRequest(uint64_t addr) {
        if (sfifo_wrapper) {
            sfifo_wrapper->addReadRequest(addr);
        }
    }
    
    // Get FIFO sizes for monitoring
    size_t getWriteFifoSize() const {
        return sfifo_wrapper ? sfifo_wrapper->getWriteFifoSize() : 0;
    }
    
    size_t getReadFifoSize() const {
        return sfifo_wrapper ? sfifo_wrapper->getReadFifoSize() : 0;
    }

public:
    uint16_t id;
    svScope scope;
    std::string scope_name;
    bool is_valid;
    SfifoHfifoWrapper* sfifo_wrapper;
};

// Example: Modern BFM Manager using the abstracted SFIFO-HFIFO
class ModernBFMManager {
private:
    std::vector<std::unique_ptr<ModernBFMContext>> contexts_;
    uint32_t num_instances_;

public:
    ModernBFMManager() : num_instances_(0) {}
    
    void initBFM(uint16_t id, uint32_t page_size_in_bit, 
                uint64_t addr_width, uint16_t data_width, 
                uint32_t cont_line_per_channel, 
                uint8_t merged_channel_num) {
        
        vpi_printf("[Modern BFM] Initializing BFM with ID=%d\n", id);
        
        svScope scope = svGetScope();
        if (!scope) {
            vpi_printf("Error: Could not get scope in initBFM\n");
            return;
        }
        
        // Create modern context with abstracted SFIFO-HFIFO
        auto ctx = std::make_unique<ModernBFMContext>(id, scope);
        contexts_.push_back(std::move(ctx));
        num_instances_++;
        
        vpi_printf("[Modern BFM] BFM instance created with ID=%d, scope=%s\n", 
                   id, svGetNameFromScope(scope));
    }
    
    ModernBFMContext* findContext(uint16_t id) {
        for (auto& ctx : contexts_) {
            if (ctx->is_valid && ctx->id == id) {
                return ctx.get();
            }
        }
        return nullptr;
    }
    
    // Example test function using the new abstracted interface
    void runModernTest() {
        vpi_printf("[Modern BFM] Starting modern test with abstracted SFIFO-HFIFO\n");
        
        for (auto& ctx : contexts_) {
            if (!ctx->is_valid) continue;
            
            // Test write operations
            for (int i = 0; i < 10; i++) {
                uint64_t addr = 0x80000000 + i * 0x20;
                std::vector<uint32_t> data = {
                    0x12345600 + i, 0x90abcdef, 0x12345678, 0x90abcdef,
                    0x12345678, 0x90abcdef, 0x12345678, 0x90abcdef
                };
                std::vector<uint32_t> mask = {0xffffffff};
                
                // Add write request - much simpler than before!
                ctx->addWriteRequest(addr, data, mask);
                
                vpi_printf("[Modern BFM] Added write request %d, FIFO size: %zu\n", 
                          i, ctx->getWriteFifoSize());
            }
            
            // Test read operations
            for (int i = 0; i < 10; i++) {
                uint64_t addr = 0x80000000 + i * 0x20;
                
                // Add read request - much simpler than before!
                ctx->addReadRequest(addr);
                
                vpi_printf("[Modern BFM] Added read request %d, FIFO size: %zu\n", 
                          i, ctx->getReadFifoSize());
            }
        }
        
        vpi_printf("[Modern BFM] Modern test completed\n");
    }
    
    static ModernBFMManager& getInstance() {
        static ModernBFMManager instance;
        return instance;
    }
};

// Example DPI-C functions using the new abstracted approach
extern "C" {
    // Modern initialization function
    void modern_init_bfm(
        uint16_t id,
        uint32_t page_size_in_bit,
        uint64_t addr_width,
        uint16_t data_width,
        uint32_t cont_line_per_channel,
        uint8_t merged_channel_num
    ) {
        vpi_printf("[Modern BFM] modern_init_bfm called with id=%d\n", id);
        ModernBFMManager::getInstance().initBFM(id, page_size_in_bit, addr_width, 
                                               data_width, cont_line_per_channel, 
                                               merged_channel_num);
    }
    
    // Modern polling functions - these are now handled automatically by the abstracted modules!
    // The hardware will call sfifo_hfifo_poll_write() and sfifo_hfifo_poll_read() automatically
    
    // Modern test function
    void modern_start_test() {
        vpi_printf("[Modern BFM] Starting modern test\n");
        
        std::thread test_thread([&]() {
            // Wait for initialization
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            ModernBFMManager::getInstance().runModernTest();
            
            // Monitor FIFO status
            for (int i = 0; i < 100; i++) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                
                // Check if all FIFOs are empty (all requests processed)
                bool all_empty = true;
                // Implementation would check all contexts...
                
                if (all_empty) {
                    vpi_printf("[Modern BFM] All requests processed, test complete\n");
                    break;
                }
            }
        });
        
        test_thread.detach();
    }
    
    // Utility function to get FIFO status
    void modern_get_fifo_status(uint16_t id, int* write_size, int* read_size) {
        ModernBFMContext* ctx = ModernBFMManager::getInstance().findContext(id);
        if (ctx) {
            *write_size = static_cast<int>(ctx->getWriteFifoSize());
            *read_size = static_cast<int>(ctx->getReadFifoSize());
        } else {
            *write_size = -1;
            *read_size = -1;
        }
    }
}

/**
 * Key Benefits of the Abstracted Approach:
 * 
 * 1. **Simplified Code**: No more manual FIFO management with mutexes and queues
 * 2. **Batch Optimization**: Automatic batching for better performance
 * 3. **Reusable**: Can be used in any project that needs SFIFO-HFIFO mechanism
 * 4. **Type Safe**: Template-based design with compile-time type checking
 * 5. **Configurable**: Easy to configure FIFO depths, batch sizes, polling intervals
 * 6. **Maintainable**: Clear separation of concerns between software and hardware sides
 * 
 * Migration Steps:
 * 
 * 1. Replace manual std::queue with SfifoHfifoWrapper
 * 2. Replace manual polling functions with automatic polling
 * 3. Use the new DPI-C interface functions
 * 4. Update SystemVerilog to use sfifo_hfifo_integrated module
 * 5. Configure parameters as needed
 */
