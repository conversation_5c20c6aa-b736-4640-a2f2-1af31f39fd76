/**
 * @file sfifo_hfifo_enhanced_tb.sv
 * @brief Enhanced SystemVerilog testbench with Reference Model and Event Communication
 *
 * This testbench demonstrates:
 * 1. Complete write-read-compare flow
 * 2. Event communication from software to hardware
 * 3. Automatic test completion detection
 * 4. Data integrity verification
 */

`timescale 1ns/1ps

// Include the abstracted SFIFO-HFIFO hardware module
`include "rtl/sfifo_hfifo_hw.sv"

module sfifo_hfifo_enhanced_tb;

    // Clock and reset
    reg clk;
    reg rstn;

    // Test parameters
    parameter INSTANCE_ID = 0;
    parameter FIFO_DEPTH = 16;
    parameter ADDR_WIDTH = 64;
    parameter DATA_WIDTH = 256;
    parameter DATA_WIDTH_IN_BYTE = DATA_WIDTH/8;
    parameter MAX_BATCH_SIZE = 4;
    parameter POLLING_INTERVAL = 50;  // Fast polling for testing

    // SFIFO-HFIFO interface signals
    wire write_req_valid;
    wire [ADDR_WIDTH-1:0] write_req_addr;
    wire [DATA_WIDTH-1:0] write_req_data;
    wire [DATA_WIDTH_IN_BYTE-1:0] write_req_mask;
    reg write_req_ready;

    wire read_req_valid;
    wire [ADDR_WIDTH-1:0] read_req_addr;
    reg read_req_ready;

    wire write_fifo_empty, write_fifo_full;
    wire read_fifo_empty, read_fifo_full;

    // Event communication interface
    wire event_req_valid;
    wire [31:0] event_type;
    wire [63:0] event_data;
    wire [31:0] event_param;
    reg event_req_ready;

    // Simple memory for demonstration
    reg [DATA_WIDTH-1:0] memory [0:1023];

    // Statistics and control
    int write_count = 0;
    int read_count = 0;
    int event_count = 0;
    reg test_completed = 0;

    // Polling counters
    reg [31:0] write_poll_counter = 0;
    reg [31:0] read_poll_counter = 0;
    reg [31:0] event_poll_counter = 0;

    // Instantiate the integrated SFIFO-HFIFO module
    sfifo_hfifo_integrated #(
        .INSTANCE_ID(INSTANCE_ID),
        .FIFO_DEPTH(FIFO_DEPTH),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .DATA_WIDTH_IN_BYTE(DATA_WIDTH_IN_BYTE),
        .POLLING_INTERVAL(POLLING_INTERVAL),
        .MAX_BATCH_SIZE(MAX_BATCH_SIZE)
    ) sfifo_hfifo_inst (
        .clk(clk),
        .rstn(rstn),

        // Write interface
        .write_req_valid(write_req_valid),
        .write_req_addr(write_req_addr),
        .write_req_data(write_req_data),
        .write_req_mask(write_req_mask),
        .write_req_ready(write_req_ready),

        // Read interface
        .read_req_valid(read_req_valid),
        .read_req_addr(read_req_addr),
        .read_req_ready(read_req_ready),

        // Status
        .write_fifo_empty(write_fifo_empty),
        .write_fifo_full(write_fifo_full),
        .read_fifo_empty(read_fifo_empty),
        .read_fifo_full(read_fifo_full)
    );

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;  // 100MHz clock
    end

    // Reset and initialization sequence
    initial begin
        rstn = 0;
        write_req_ready = 1;
        read_req_ready = 1;
        event_req_ready = 1;

        #100;
        rstn = 1;

        $display("[TB] Reset completed, starting enhanced SFIFO-HFIFO test");

        // Initialize enhanced BFM
        enhanced_init_bfm(INSTANCE_ID, 256*32, 64, 256, 8, 2);

        #200;

        // Start enhanced test
        $display("[TB] Starting enhanced test with Reference Model");
        enhanced_start_test();
    end

    // Handle write requests
    always @(posedge clk) begin
        if (rstn && write_req_valid && write_req_ready) begin
            // Apply mask and write to memory
            for (int i = 0; i < DATA_WIDTH_IN_BYTE; i++) begin
                if (write_req_mask[i]) begin
                    memory[write_req_addr[9:0]][i*8 +: 8] <= write_req_data[i*8 +: 8];
                end
            end

            write_count <= write_count + 1;
            $display("[TB] Write processed: addr=0x%x, data=0x%x, count=%d",
                    write_req_addr, write_req_data, write_count + 1);
        end
    end

    // Handle read requests and send responses
    always @(posedge clk) begin
        if (rstn && read_req_valid && read_req_ready) begin
            // Read from memory
            automatic reg [DATA_WIDTH-1:0] read_data;
            read_data = memory[read_req_addr[9:0]];

            read_count <= read_count + 1;
            $display("[TB] Read processed: addr=0x%x, data=0x%x, count=%d",
                    read_req_addr, read_data, read_count + 1);

            // Send read response back to software (with delay to simulate pipeline)
            send_read_response(read_req_addr, read_data);
        end
    end

    // Event polling logic
    always @(posedge clk) begin
        if (rstn) begin
            event_poll_counter <= event_poll_counter + 1;

            if (event_poll_counter >= POLLING_INTERVAL + 10) begin  // Slightly offset
                automatic int event_available;
                automatic int evt_type;
                automatic longint unsigned evt_data;
                automatic int evt_param;

                event_poll_counter <= 0;
                event_available = h2s_polling_event(INSTANCE_ID, evt_type, evt_data, evt_param);

                if (event_available) begin
                    event_count <= event_count + 1;
                    $display("[TB] Event received: type=%d, data=0x%x, param=%d, count=%d",
                            evt_type, evt_data, evt_param, event_count + 1);

                    // Handle different event types
                    case (evt_type)
                        0: begin  // TEST_COMPLETE
                            $display("[TB] ========================================");
                            $display("[TB] TEST COMPLETION EVENT RECEIVED!");
                            $display("[TB] ========================================");
                            test_completed <= 1;

                            // Wait a bit then finish
                            #1000;
                            $display("[TB] Final Statistics:");
                            $display("[TB]   Writes processed: %d", write_count);
                            $display("[TB]   Reads processed:  %d", read_count);
                            $display("[TB]   Events received:  %d", event_count);
                            $display("[TB] ========================================");
                            $display("[TB] ENHANCED TEST PASSED!");
                            $display("[TB] ========================================");
                            $finish;
                        end

                        1: begin  // ERROR_DETECTED
                            $display("[TB] ========================================");
                            $display("[TB] ERROR EVENT RECEIVED!");
                            $display("[TB] Error count: %d", evt_data);
                            $display("[TB] Total reads: %d", evt_param);
                            $display("[TB] ========================================");
                            test_completed <= 1;

                            #1000;
                            $display("[TB] ENHANCED TEST FAILED!");
                            $finish;
                        end

                        2: begin  // STATUS_UPDATE
                            $display("[TB] Status update: data=0x%x, param=%d", evt_data, evt_param);
                        end

                        default: begin
                            $display("[TB] Unknown event type: %d", evt_type);
                        end
                    endcase
                end
            end
        end
    end

    // Status monitoring
    reg [31:0] status_counter = 0;
    always @(posedge clk) begin
        if (rstn && !test_completed) begin
            status_counter <= status_counter + 1;

            if (status_counter >= 10000) begin  // Every 100us
                automatic int write_fifo_size, read_fifo_size, test_complete, error_count;

                status_counter <= 0;
                enhanced_get_status(INSTANCE_ID, write_fifo_size, read_fifo_size, test_complete, error_count);

                if (write_fifo_size > 0 || read_fifo_size > 0 || test_complete > 0) begin
                    $display("[TB] Status @ %t: Write FIFO=%d, Read FIFO=%d, Complete=%d, Errors=%d",
                            $time, write_fifo_size, read_fifo_size, test_complete, error_count);
                end
            end
        end
    end

    // Task to send read response
    task send_read_response(input [ADDR_WIDTH-1:0] addr, input [DATA_WIDTH-1:0] data);
        automatic int unsigned data_words = DATA_WIDTH / 32;
        automatic int unsigned data_array[0:7];  // Assuming 8 words for 256-bit data

        // Convert data to array
        for (int i = 0; i < data_words; i++) begin
            data_array[i] = data[i*32 +: 32];
        end

        // Send response to software
        h2s_read_data_resp(INSTANCE_ID, addr, data_array, data_words);

        $display("[TB] Sent read response: addr=0x%x, data[0]=0x%x", addr, data_array[0]);
    endtask

    // Timeout protection
    initial begin
        #500000;  // 5ms timeout
        if (!test_completed) begin
            $display("[TB] ERROR: Test timeout! Enhanced test did not complete in time.");
            $display("[TB] Current status:");
            $display("[TB]   Writes processed: %d", write_count);
            $display("[TB]   Reads processed:  %d", read_count);
            $display("[TB]   Events received:  %d", event_count);
            $finish;
        end
    end

    // DPI-C function imports
    import "DPI-C" context function void enhanced_init_bfm(
        input shortint unsigned id,
        input int unsigned page_size_in_bit,
        input longint unsigned addr_width,
        input shortint unsigned data_width,
        input int unsigned cont_line_per_channel,
        input byte unsigned merged_channel_num
    );

    import "DPI-C" context function void enhanced_start_test();

    import "DPI-C" context function void h2s_read_data_resp(
        input shortint unsigned instance_id,
        input longint unsigned addr,
        input int unsigned data[],
        input int data_words
    );

    import "DPI-C" context function int h2s_polling_event(
        input shortint unsigned instance_id,
        output int event_type,
        output longint unsigned event_data,
        output int unsigned event_param
    );

    import "DPI-C" context function void enhanced_get_status(
        input shortint unsigned id,
        output int write_size,
        output int read_size,
        output int test_complete,
        output int error_count
    );

    // Optional: VCD dump for debugging
    `ifdef DUMP_VCD
    initial begin
        $dumpfile("sfifo_hfifo_enhanced.vcd");
        $dumpvars(0, sfifo_hfifo_enhanced_tb);
        $display("[TB] VCD dumping enabled: sfifo_hfifo_enhanced.vcd");
    end
    `endif

endmodule

/**
 * This enhanced testbench demonstrates:
 *
 * 1. **Complete Verification Flow**:
 *    - Write data to RTL and Reference Model
 *    - Read data from RTL
 *    - Compare RTL data with Reference Model
 *    - Report PASS/FAIL for each transaction
 *
 * 2. **Event Communication**:
 *    - Software signals test completion to hardware
 *    - Hardware polls for events and handles them
 *    - Automatic test termination based on events
 *
 * 3. **Data Integrity Checking**:
 *    - Reference Model maintains expected data
 *    - Automatic comparison of read responses
 *    - Error counting and reporting
 *
 * 4. **Enhanced Monitoring**:
 *    - Real-time status reporting
 *    - Progress tracking
 *    - Timeout protection
 *
 * Key Improvements over Basic Version:
 * - Reference Model ensures data correctness
 * - Event system enables proper test completion
 * - Automatic verification reduces manual checking
 * - Complete end-to-end verification flow
 *
 * Usage:
 *   make enhanced_lib
 *   make enhanced_comp
 *   make enhanced_run
 */
