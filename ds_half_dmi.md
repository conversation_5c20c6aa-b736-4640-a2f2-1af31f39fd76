# HBM3 Mirror Memory Dynamic Storage VP Controller Algorithm Specification

## 1. Core Concepts

- Blocking access for both sides
- VP make the request to switch the page
- All pages initially in VP side

### 1.1 Page Definition

- Basic unit: 4KB (32-byte) page (default)
- Each page has independent state tracking

### 1.2 System Architecture

```
+------------------+        +----------------+       +----------------+
|       Proxy      |        |     Bridge     |       |  Hardware Side |
| (Sparse Memory)  | <----> |      DMI       |<----> |  (Full Memory) |
|     State Table  |        |                |       |  State Table   |
+------------------+        +----------------+       +----------------+
```

### 1.3 Key Characteristics

#### Initial State
- All pages are initially in VP side
- Two key limitations:
  1. HBM3 image can only be loaded from VP side
  2. <PERSON><PERSON> must wait for VP's launch before starting operation

#### Memory Organization
- **Dual-Side Storage**: Hardware side maintains full memory space, VP side implements sparse storage
- **Page-Level Granularity**: Fixed 4KB page size with aligned access
- **Active Page Location**: Real-time tracking of page ownership between VP and Hardware sides

#### Memory Bridge Capabilities
- **Hardware Access**: Blocking DPI-C for accessing VP-side pages
- **VP-Side Access**: DMI-based communication for reading/writing HW-side pages
- **Bidirectional Transfer**: Direct data movement between VP and Hardware memory spaces

#### Page Switch Decision Making
- **VP to HW**: Decisions made by Proxy based on access patterns
- **HW to VP**: Decisions made by Bridge based on access patterns
- **Initial Implementation**: Fixed ratio for migration decisions
- **Future Enhancement**: Adaptive ratio based on access patterns

#### State Management
- **Real-time Location State**: Immediate consistency of page location information
- **Centralized Control**: Bridge manages state transitions and consistency
- **Independent Access Paths**: Direct memory operations for active side

#### Assumptions

- **sw call export function**:  non-blocking
- **Co-Emu DMI**: blocking

#### Key characteristics

- **VP DMI**:  At beginning, HW_DMI_THRESHOLD is 0.  Once HW access the pages bigger than HW_DMI_THRESHOLD, VP DMI will be invalid. So VP can compare the rate of increase of access count from HW and VP side in 100us, if the rate of HW access is low, then VP can re-open the DMI, and increase the HW_DMI_THRESHOLD to the HW access count in last 100us.
- **Page Migration**: every time, the next PageNeighbor pages will be migrated to the active side.
- **PageNeighbor**: the neighbor pages of the current page. It's adaptive. VP will change it as to the previous access pattern.
  - When Page is in VP side: VP tracks the number of consecutive pages accessed by hardware side previously, and uses this to adjust how many PageNeighbors to switch to hardware side
  - When Page is in HW side: VP tracks the number of consecutive pages it accessed in hardware previously, and uses this to adjust how many PageNeighbors to request switching back to software side
- **Address Mapping**: need the mapping from rbc_address to system_address in BFM

## 2. Scenario

### 2.1 Launch Sequence
- Initial state: All pages are in VP by default
- VP loads image into its pages
- HW starts operation after VP launch completes

### 2.2 Page in VP
- VP access directly
- HW access through blocking DPI-C
- HW access increases access count
- VP makes decision to switch page based on access patterns
- Bridge handles page transfer to HW through DMI
- Bridge completes blocking DPI-C call

### 2.3 Page in HW
- HW access directly
- Bridge informs Proxy to stop VP access to HW pages
- Bridge requests page switch from HW
- HW responds (blocking call)
- Bridge uses DMI to execute page switch
- Bridge updates state tables

## 3. State Tables

### 3.1 Table Designs

#### 3.1.1 VP-Side Table (example)

```cpp
class VPLocationTable {
    struct PageEntry {
        bool is_hw_active;           // Real-time page location indicator
        std::atomic<uint32_t> hw_access_count;  // Non-blocking local counter
        std::atomic<uint32_t> vp_access_count;  // Non-blocking local counter
        uint64_t last_report_count;  // Last reported count to bridge
        Page* page_ptr;              // Local page data pointer, nullptr if in HW
    };
    
    std::unordered_map<uint64_t, PageEntry> page_table;  // Sparse page table
    mutable std::shared_mutex location_mutex;  // RW lock for location updates
    
    // Threshold-based migration check
    bool should_migrate(uint64_t page_addr) {
    }
};
// request to get the HW page
request_hw_page(addr);
// put the page to HW
put_hw_page(addr, data, size);
```

#### 3.1.2 Bridge Table

no talbe

#### 3.1.3 Hardware-Side Table (example)

```verilog
reg [63:0] page_table[0:PAGE_NUM-1];  // record the access count from last switch
```

64 bit:

```wavedrom
{reg:
[
    { "name": "AccessCount",   "bits": 62, "attr": "RW" },
    { "name": "InHW",   "bits": 1, "attr": "RW" },
    { "name": "Written",   "bits": 1, "attr": "RW" }
]
}
```

### 3.2 Table Characteristics

#### 3.2.1 VP-Side Table

- Sparse implementation
- Stores pointers only for VP-side active pages
- Local access counting for optimization
- Dynamic entry creation and deletion

#### 3.2.2 RTL Table

- Fixed-size hardware table
- Stores full location information
- Optimized for fast queries
- local count record
- Hardware-optimized implementation

### 3.3 Page Synchronization

#### 3.3.1 Location Synchronization

### 3.4 VP-Side Page Management

#### 3.4.1 Page Lifecycle

1. Page Creation:
   - Check pool for available page
   - Reuse from pool if available
   - Grow pool if empty and under MAX_POOL_SIZE
   - Allocate new page as last resort

2. Page Migration to HW:
   - Copy data to hardware
   - Mark page as HW active
   - Return page to pool but retain data
   - Update last_used_time (optional)

3. Page Migration to VP:
   - Acquire page from pool
   - Copy data from hardware
   - Mark page as VP active
   - Update last_used_time (optional)

4. Pool Management:
   - LRU-based page selection
   - Periodic pool size optimization
   - Optional background cleanup

### 3.5 Memory Optimization

1. Pool Size Management:
   - Start with reasonable initial size
   - Grow on demand up to maximum
   - Shrink under memory pressure
   - Monitor usage patterns

2. Page Reuse Strategy:
   - Prefer pages with matching address
   - Use LRU for general allocation

3. Performance Considerations:
   - Thread-safe pool operations
   - Fast path for hot pages
   - Batch pool management
   - Minimize lock contention

## 4. Page Management

### 4.1 Access Recording

1. VP-side Access:
   - Immediate local counter update
   - Asynchronous notification to Bridge
   - No wait for confirmation

2. HW-side Access:
   - Non-blocking DPI-C counter update
   - Local RTL counter increment
   - Periodic Bridge notification

### 4.2 Page Switch Decision

1. Decision Making:
   - Made only by VP side
   - Conservative threshold
   - HW could refuse the switch request
   - Ensures atomic switches

2. Switch Process:

VP send page to HW

- VP send the page to Bridge
- Bridge use DMI to put the page from HW
- At the same time, HW needs update the location information (export call is non-blocking)

VP request the page from HW

- VP send the request to Bridge and HW
- HW send ack to Bridge
- Bridge use DMI to get the page from HW
- At the same time, HW needs update the location information (export call is non-blocking)
- Bridge send the page to VP

### 4.3 Switch Process

1. Preparation:
   - Block new access requests to the page
   - Complete pending operations
   - Verify state table consistency

2. Data Transfer:
   - Copy page data to new active side
   - Update state tables on both sides
   - Verify transfer completion

3. Activation:
   - Update active_side in state tables
   - Resume access processing
   - Start new frequency counting window

## 5. Implementation Considerations

### 5.1 Critical Sections

- State table updates
- Page switch process
- Access counter updates
- Consistency verification

### 5.2 Performance Optimization Direction

- Batch processing for state updates
- Predictive page switching
- Cache frequently accessed state entries
- Optimize data transfer paths

### 5.3 Error Handling

1. State Table Inconsistency:
   - Regular consistency checks
   - Logging for debugging

### 5.4 Latency Considerations

1. Fast Path (Active Side Access):
   - Direct memory access
   - Local counter update only
   - No bridge communication

2. Slow Path (Inactive Side Access):
   - Bridge communication required
   - Counter updates on both sides
   - Potential page switch overhead

## 6. Bridge Interface

### 6.1 DPI-C Functions

```c
// Forward declarations for dmi functions
extern "C" {
    extern int dmi_get_hdl_handle(const char * hdl_mem_path);
    extern int dmi_get_size(int hdl_handle, unsigned int* width, unsigned int* depth);
    extern int dmi_new_c_handle(const unsigned int width, const unsigned long long depth);
    extern int dmi_write_word(int c_buffer_handle, const unsigned long long addr, const unsigned int * data);
    extern int dmi_read_word(int c_buffer_handle, const unsigned long long addr, unsigned int * data);
    extern int dmi_write_block(int c_buffer_handle, const unsigned long long addr, const unsigned int * data, const int len);
    extern int dmi_read_block(int c_buffer_handle, const unsigned long long addr, const unsigned int * data, const int len);
    extern int dmi_to_hw(int c_buffer_handle, unsigned long long c_buffer_addr, int hdl_mem_handle, unsigned long long hdl_mem_addr, int len);
    extern int dmi_from_hw(int c_buffer_handle, unsigned long long c_buffer_addr, int hdl_mem_handle, unsigned long long hdl_mem_addr, int len);
    extern int dmi_from_hw_mem_path(char* c_buffer, const char* hdl_mem_path, unsigned long long hdl_mem_addr, unsigned long long len);
    extern int dmi_to_hw_mem_path(const char* c_buffer, const char* hdl_mem_path, unsigned long long hdl_mem_addr, unsigned long long len);
 
    extern void umi_stop_clock()
    extern void umi_resume_clock()
    extern umi_force();
    extern umi_release();
}
// put page to HW, and set the flags
dmi_get_hdl_handle(vmem_path);
dmi_get_size(vm_handle, width, depth);
dmi_new_c_handle(width, depth);
dmi_write_block(c_handle, addr, data, len);
umi_stop_clock();
dmi_to_hw(c_handle, c_addr, hdl_handle, hdl_addr, len);
umi_resume_clock();

// get page from HW, and put it to VP
umi_stop_clock();
dmi_get_hdl_handle(vmem_path);
dmi_get_size(vm_handle, width, depth);
dmi_new_c_handle(width, depth);
dmi_from_hw(c_handle, c_addr, hdl_handle, hdl_addr, len);
umi_resume_clock();
ipc_send_page_to_vp(addr, data);
```

### 6.2 Communication Protocol

1. Normal Access:
   - Check local state table
   - Update access counter

2. Page Switch:
   - Request switch through bridge
   - Wait for acknowledgment
   - Perform data transfer
   - Update state tables
   - Resume operation

## 7. BFM Interface

### 7.1 Module Parameters
```verilog
parameter [9:0] HBM_ID = 0;
parameter [4:0] CHANNEL_ID = 0;
parameter [0:0] PC_ID = 0;
parameter ADDR_WIDTH = 64;
parameter DATA_WIDTH = 256;
parameter BASE_ADDR = 64'h0000000000000000;
parameter PAGE_SIZE = 4096*8;
parameter PAGE_SWITCH_RATIO = 2;  // When VP_ACCESS * ratio > HW_ACCESS, switch page
parameter DPIC_WFIFO_DEPTH = 16;
parameter DPIC_RFIFO_DEPTH = 16;
parameter DATA_BYTE_WIDTH = DATA_WIDTH/8;
```

### 7.2 Memory Interface
```verilog
// Write Port
input  wire                           mm_clk,
input  wire                           mm_rstn,
input  wire                           mm_wen,
input  wire [ADDR_WIDTH-1:0]         mm_waddr,
input  wire [DATA_WIDTH-1:0]         mm_wdata,
input  wire [DATA_BYTE_WIDTH-1:0]    mm_wmask,

// Read Port
input  wire                           mm_ren,
input  wire [ADDR_WIDTH-1:0]         mm_raddr,
output reg  [DATA_WIDTH-1:0]         mm_rdata,
output reg                           mm_rvalid
```

### 7.3 DPI-C Interface Functions

#### 7.3.1 Initialization Functions
```verilog
// Initialize BFM with ID and page size
import "DPI-C" context function void init_bfm(
    input bit [15:0] id,
    input bit [63:0] page_size_in_bit
);

// Reset page table
import "DPI-C" context function void reset_page_table(
    input bit [15:0] id
);
```

#### 7.3.2 HW to VP Access Functions
```verilog
// HW write to VP page
import "DPI-C" context function void h2s_pc_write(
    input bit [15:0] id,
    input bit [ADDR_WIDTH-1:0] addr,
    input bit [DATA_WIDTH-1:0] data,
    input int unsigned data_size_in_bits,
    input bit [DATA_BYTE_WIDTH-1:0] mask,
    output bit done
);

// HW read from VP page
import "DPI-C" context function void h2s_pc_read(
    input bit [15:0] id,
    input bit [ADDR_WIDTH-1:0] addr,
    input int unsigned data_size_in_bits,
    output bit [DATA_WIDTH-1:0] data
);
```

#### 7.3.3 VP to HW Access Functions
```verilog
// VP write request to HW page
export "DPI-C" function s2h_pc_write_req;
function automatic void s2h_pc_write_req(
    input bit [ADDR_WIDTH-1:0] addr,
    input bit [DATA_WIDTH-1:0] data,
    input int unsigned data_size_in_bits,
    input bit [DATA_BYTE_WIDTH-1:0] mask
);

// VP read request to HW page
export "DPI-C" function s2h_pc_read_req;
function automatic void s2h_pc_read_req(
    input bit [ADDR_WIDTH-1:0] addr,
    input int unsigned data_size_in_bits
);

// Response functions for VP requests
(* is_blocking_dpi=1 *)
import "DPI-C" context function void h2s_pc_write_resp(
    input bit [15:0] id,
    input bit [ADDR_WIDTH-1:0] addr
);

(* is_nonblocking_dpi=1 *)
import "DPI-C" context function void h2s_pc_read_resp(
    input bit [15:0] id,
    input bit [ADDR_WIDTH-1:0] addr,
    input int unsigned data_size_in_bits,
    input bit [DATA_WIDTH-1:0] data
);
```

#### 7.3.4 Page Access Notification Functions
```verilog
// Notify VP about HW page access count
import "DPI-C" context function void h2s_notify_page_access_count(
    input bit [63:0] page_id,
    input bit [31:0] access_count
);
```

#### 7.3.5 Page Switch Functions
```verilog
// VP requests page switch
export "DPI-C" function h2s_switch_page_request;
function automatic void h2s_switch_page_request(
    input bit [63:0] page_id,
    input bit [32:0] page_number
);

// HW responds to page switch request
import "DPI-C" context function void h2s_switch_page_response();
```

### 7.4 Interface Usage Notes

1. Initialization Sequence:
   - Call `init_bfm()` with appropriate ID and page size
   - Call `reset_page_table()` to initialize page states
   - Configure notification interval if needed

2. Access Flow:
   - HW to VP: Use h2s_pc_read/write for direct access
   - VP to HW: Use s2h_pc_read/write_req for request, wait for response
   - Access notifications sent automatically based on configured interval

3. Page Switching:
   - VP initiates with h2s_switch_page_request
   - HW completes switch and calls h2s_switch_page_response
   - All operations must complete before switch

4. Important Considerations:
   - All VP write requests are non-blocking
   - Read responses are asynchronous
   - Page switches require all pending operations to complete
   - Access counts are maintained separately for read/write

## 8. Monitoring and Debug

### 8.1 Performance Metrics

- Page switch frequency
- Access patterns
- Transfer latencies
- Counter statistics

### 8.2 Debug Interface

- State table dumps
- Access history
- Switch event logs
- Error tracking

## 9. Adaptive Threshold

### 9.1 Initial Implementation
- Fixed ratio for migration decisions at beginning
- Configurable thresholds for both VP2HW and HW2VP
- Simple access count comparison

### 9.2 Future Enhancement
- Adaptive ratio based on access patterns
- Dynamic threshold adjustment
- Performance monitoring and optimization

### 9.3 Scenario

**S1**:

- HW write 4 bytes to the page
- VP read 4 bytes from the page
- VP write 4 bytes to the page
- HW read 4 bytes from the page
- repeat the above steps

**S2**:

- HW write 4M bytes to the pages
- VP read 4M bytes from the pages
- VP write 4M bytes to the pages
- HW read 4M bytes from the pages
- repeat the above steps

**S3**:

- HW write 40M bytes to the pages
- VP read 40M bytes from the pages
- VP write 40M bytes to the pages
- HW read 40M bytes from the pages
- repeat the above steps

**S4**:

- HW writes 4M bytes to the pages
- VP reads 128 bytes from the page
- repeat the above steps

**S5**:

- VP writes 4M bytes to the pages
- HW reads 128 bytes from the page
- repeat the above steps

**S6**:

- VP write 256 bytes to the page
- HW read 256 bytes from the page
- VP write 1M bytes to the pages, HW write 2M bytes to the pages
- VP read 2M bytes from the pages, HW read 1M bytes from the pages
- repeat the above steps

### 9.4 Scenario example

- VP owns VP2HW and HW2VP threshold
- default VP2HW threshold: 1
- default HW2VP threshold: 1
- default PageNeighbor: 15

- page is in VP
- VP receive the access from HW, invalidate DMI, then clear the count
- HW read access count: 1, VP access count: 0
- VP switches the 16 pages to HW

- page is in HW
- VP access count: 0
- HW continues to read,  access count: 31
- HW receive the access from VP, then clear the count
- VP write access count: 1, HW access count: 0
- HW switch the pages to VP

---

## Appendix

$4*1024/256 = 16$

$16 * 15 = 240 us$

100 us内统计访问增涨速率，以此来调整切换threshold

---

## Reference code

```cpp
class PageTracker {
    struct AccessRecord {
        uint64_t start_page;     // First page in sequence
        uint32_t page_count;     // Number of consecutive pages
        uint64_t timestamp;      // For tracking access timing
    };

    static constexpr uint64_t PAGE_SIZE = 4096;
    static constexpr size_t HISTORY_SIZE = 100;  // Keep last N access patterns

    std::deque<AccessRecord> access_history_;
    uint64_t current_start_page_ = 0;
    uint32_t current_consecutive_count_ = 0;
    uint64_t last_access_time_ = 0;
    static constexpr uint64_t ACCESS_TIMEOUT_NS = 100000;  // 100us timeout

public:
    void record_access(uint64_t address, uint64_t current_time_ns) {
        uint64_t page_id = address / PAGE_SIZE;
        
        if (current_consecutive_count_ == 0) {
            // Start new sequence
            current_start_page_ = page_id;
            current_consecutive_count_ = 1;
            last_access_time_ = current_time_ns;
            return;
        }

        // Check if this is next consecutive page and within timeout
        if (page_id == current_start_page_ + current_consecutive_count_ && 
            (current_time_ns - last_access_time_) < ACCESS_TIMEOUT_NS) {
            current_consecutive_count_++;
        } else {
            // Save previous sequence and start new one
            access_history_.push_front({
                current_start_page_,
                current_consecutive_count_,
                last_access_time_
            });

            if (access_history_.size() > HISTORY_SIZE) {
                access_history_.pop_back();
            }

            current_start_page_ = page_id;
            current_consecutive_count_ = 1;
        }
        
        last_access_time_ = current_time_ns;
    }

    // Get average consecutive pages from recent history
    uint32_t get_average_consecutive_pages() const {
        if (access_history_.empty()) return 1;

        uint64_t total = 0;
        for (const auto& record : access_history_) {
            total += record.page_count;
        }
        return total / access_history_.size();
    }
};
```

functionalities:

1. Tracks consecutive page accesses with a timeout window (100μs) to determine if accesses are part of the same sequence
2. Maintains a history of recent access patterns (last 100 patterns)`
3. Can calculate average consecutive page count from the history
4. Uses page_id to determine if accesses are consecutive

Key features:

1. Records start page and count for each sequence
2. Uses timestamps to determine if accesses are part of same sequence
3. Maintains fixed-size history using deque
4. Provides method to get average consecutive pages accessed

## Runtime Configuration

The BFM includes a notification interval register that can be adjusted at runtime:
- Register name: rtl_notify_interval
- Default value: 10 cycles
- Can be modified using force command to adjust notification frequency

## Key Features

1. Memory Access
2. Conflict Resolution
3. Page Migration
   - Dynamic page location tracking
   - Access counting for both HW and VP
   - RTL access notification with configurable interval
   - Software-side access pattern analysis
   - Migration decisions based on combined access data
4. Performance
   - Non-blocking DPI-C interface
