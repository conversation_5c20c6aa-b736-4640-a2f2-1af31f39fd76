# HBM3 BFM Design Specification

## 1. Core Functionality

### 1.1 Page Management

- Basic unit: 4KB (32-byte aligned) page
- Page size configurable through parameter PAGE_SIZE (default: 4096*8 bits)
- Page switching based on access ratio (configurable through PAGE_SWITCH_RATIO)
- Maintains page table with access statistics and state flags
- Supports both hardware (HW) and virtual platform (VP) page storage
- Supports adaptive PageNeighbor for batch page migration
- Supports VP DMI threshold adjustment

### 1.2 Address Handling

```txt
[ADDR_WIDTH-1:0] mm_addr
[63:0] sys_addr
[63:0] mem_addr
[63:0] page_addr

Page Table Entry (64-bit):
[61:0] - Access Count
[62]   - Written flag (1: page modified, 0: clean)
[63]   - InHW flag (1: page in HW, 0: page in VP)
```

### 1.3 State Management

#### 1.3.1 Read Path FSM
- ST_RTL_READ_IDLE: Initial state, waiting for read request
- ST_RTL_READ_ADDR: Address comparison and page table lookup
- ST_RTL_READ_DONE: Complete read operation and update statistics

#### 1.3.2 Write Path FSM
- ST_RTL_WRITE_IDLE: Initial state, waiting for write request
- ST_RTL_WRITE_ADDR: Address processing and page table update
- ST_RTL_WRITE_DONE: Complete write operation

#### 1.3.3 Page Switch FSM
- SWITCH_PAGE_INIT: Initial state, waiting for switch request
- SWITCH_PAGE_WAIT: Wait for ongoing operations to complete
- SWITCH_PAGE_CLEAN: Perform page switch and cleanup

### 1.4 Adaptive Mechanisms

#### 1.4.1 VP DMI Control
- Initial HW_DMI_THRESHOLD set to 0
- VP monitors HW access rate in 100us windows
- Adjusts DMI threshold based on HW access patterns
- Can re-enable DMI if HW access rate is low

#### 1.4.2 PageNeighbor Adaptation
- Tracks consecutive page access patterns
- Adjusts PageNeighbor count based on access history
- Separate tracking for HW and VP access patterns
- Default PageNeighbor: 15 pages

## 2. Interface Requirements

### 2.1 Memory Interface

```verilog
// Write Port
input  wire                           mm_clk,
input  wire                           mm_rstn,
input  wire                           mm_wen,
input  wire [ADDR_WIDTH-1:0]         mm_waddr,
input  wire [DATA_WIDTH-1:0]         mm_wdata,
input  wire [DATA_BYTE_WIDTH-1:0]    mm_wmask,

// Read Port
input  wire                           mm_ren,
input  wire [ADDR_WIDTH-1:0]         mm_raddr,
output reg  [DATA_WIDTH-1:0]         mm_rdata,
output reg                           mm_rvalid
```

### 2.2 DPI-C Interface

#### 2.2.1 Core Functions
```verilog
// Initialization
import "DPI-C" context function void init_bfm(
    input bit [15:0] id,
    input bit [63:0] page_size_in_bit
);

import "DPI-C" context function void reset_page_table(
    input bit [15:0] id
);

// Page Access Functions
import "DPI-C" context function void h2s_pc_write(
    input bit [15:0] id,
    input bit [ADDR_WIDTH-1:0] addr,
    input bit [DATA_WIDTH-1:0] data,
    input int unsigned data_size_in_bits,
    input bit [DATA_BYTE_WIDTH-1:0] mask,
    output bit done
);

import "DPI-C" context function void h2s_pc_read(
    input bit [15:0] id,
    input bit [ADDR_WIDTH-1:0] addr,
    input int unsigned data_size_in_bits,
    output bit [DATA_WIDTH-1:0] data
);
```

#### 2.2.2 VP Access to HW Pages
```verilog
// VP request functions
export "DPI-C" function s2h_pc_write_req;
export "DPI-C" function s2h_pc_read_req;

// VP write request handling
function automatic void s2h_pc_write_req(
    input bit [ADDR_WIDTH-1:0] addr,
    input bit [DATA_WIDTH-1:0] data,
    input int unsigned data_size_in_bits,
    input bit [DATA_BYTE_WIDTH-1:0] mask
);

// VP read request handling
function automatic void s2h_pc_read_req(
    input bit [ADDR_WIDTH-1:0] addr,
    input int unsigned data_size_in_bits
);

// Response functions for VP requests
(* is_blocking_dpi=1 *)
import "DPI-C" context function void h2s_pc_write_resp(
    input bit [15:0] id,
    input bit [ADDR_WIDTH-1:0] addr
);

(* is_nonblocking_dpi=1 *)
import "DPI-C" context function void h2s_pc_read_resp(
    input bit [15:0] id,
    input bit [ADDR_WIDTH-1:0] addr,
    input int unsigned data_size_in_bits,
    input bit [DATA_WIDTH-1:0] data
);
```

#### 2.2.3 Access Notification

```verilog
import "DPI-C" context function void h2s_notify_page_access_count(
    input bit [63:0] page_id,
    input bit [31:0] access_count
);
```

### 2.3 Access Control and FIFO Management

#### 2.3.1 FIFO Parameters and Structures
```verilog
// FIFO depths
parameter DPIC_WFIFO_DEPTH = 16
parameter DPIC_RFIFO_DEPTH = 16
parameter PAGE_SWITCH_FIFO_DEPTH = 16

// VP write request FIFO structures
reg [DATA_WIDTH-1:0] dpi_wdata_fifo [0: DPIC_WFIFO_DEPTH-1];
reg [64-1:0] dpi_waddr_fifo [0: DPIC_WFIFO_DEPTH-1];
reg [DATA_BYTE_WIDTH-1:0] dpi_wmask_fifo [0: DPIC_WFIFO_DEPTH-1];

// VP read request FIFO structures
reg [64-1:0] dpi_raddr_fifo [0: DPIC_RFIFO_DEPTH-1];

// FIFO pointers
reg [DPIC_WFIFO_AWIDTH:0] dpi_wfifo_rptr;
reg [DPIC_WFIFO_AWIDTH:0] dpi_wfifo_wptr;
reg [DPIC_RFIFO_AWIDTH:0] dpi_rfifo_rptr;
reg [DPIC_RFIFO_AWIDTH:0] dpi_rfifo_wptr;
```

#### 2.3.2 VP Access State Machine
```verilog
// VP read states
localparam ST_DPI_READ_IDLE = 0;
localparam ST_DPI_READ_ADDR = 1;
localparam ST_DPI_READ_DATA = 2;
localparam ST_DPI_READ_DONE = 3;

// VP write states
localparam ST_DPI_WRITE_IDLE = 0;
localparam ST_DPI_WRITE_ADDR = 1;
localparam ST_DPI_WRITE_DATA = 2;
localparam ST_DPI_WRITE_DONE = 3;
```

#### 2.3.3 VP Access Processing Flow

1. VP Write Request Processing:
   - VP calls `s2h_pc_write_req` to initiate write request
   - Request data is stored in write FIFO
   - Write FSM processing flow:
     * IDLE: Check FIFO not empty, transition to ADDR state
     * ADDR: Fetch FIFO data, prepare for write
     * DATA: Execute actual memory write
     * DONE: Call `h2s_pc_write_resp` to signal completion

2. VP Read Request Processing:
   - VP calls `s2h_pc_read_req` to initiate read request
   - Request address is stored in read FIFO
   - Read FSM processing flow:
     * IDLE: Check FIFO not empty, transition to ADDR state
     * ADDR: Fetch FIFO address, prepare for read
     * DATA: Read data from memory
     * DONE: Call `h2s_pc_read_resp` to return data

3. FIFO Management:
   - Use separate read/write FIFOs to avoid blocking
   - Implement FIFO full detection to prevent overflow
   - Support dynamic space calculation:
     ```verilog
     bit [DPIC_WFIFO_AWIDTH:0] dpi_wleft_space;
     bit [DPIC_RFIFO_AWIDTH:0] dpi_rleft_space;
     bit dpic_wfifo_empty;
     bit dpic_rfifo_empty;
     ```

#### 2.3.4 Access Counting and Notification
- Configurable notification interval (default: 10 cycles)
- Separate counters for read and write access
- Notification triggers:
  1. Counter reaches interval
  2. Page switch occurs
  3. Operation completes

#### 2.3.5 Page Switching Mechanism
```verilog
// Page switch states
localparam SWITCH_PAGE_INIT = 0;
localparam SWITCH_PAGE_WAIT = 1;
localparam SWITCH_PAGE_CLEAN = 2;

// Page switch FIFO structures
bit [63:0] page_id_fifo [0: PAGE_SWITCH_FIFO_DEPTH-1];
bit [32:0] page_number_fifo [0: PAGE_SWITCH_FIFO_DEPTH-1];
bit [PAGE_SWITCH_FIFO_AWIDTH:0] page_switch_fifo_rptr;
bit [PAGE_SWITCH_FIFO_AWIDTH:0] page_switch_fifo_wptr;
```

1. Page Switch Trigger:
   - Based on access ratio between HW and VP
   - Configurable through PAGE_SWITCH_RATIO parameter
   - VP side requests switch through `h2s_switch_page_request`

2. Switch Process:
   - INIT state:
     * Check page switch FIFO not empty
     * If request exists, transition to WAIT state
   
   - WAIT state:
     * Wait for all ongoing operations to complete:
       - read_is_done
       - write_is_done
       - dpic_wfifo_empty && no response
       - dpic_rfifo_empty && no response
     * Transition to CLEAN state when all operations complete

   - CLEAN state:
     * Execute page switch
     * Call blocking `h2s_switch_page_response` to notify completion
     * Bridge completes page switch in `h2s_switch_page_response`
     * Return to INIT state

3. Safety Guarantees:
   - Use FIFO to manage switch requests
   - Ensure all operations complete before switching
   - Maintain atomicity of switch process

## 3. Implementation Guidelines

### 3.1 FSM Implementation
- Clear state definitions with enumerated types
- Single-cycle state transitions
- Separate FSMs for read, write, and page switching
- Clean state reset behavior

### 3.2 Memory Access Rules
- Independent read and write paths
- Page-level access tracking
- Atomic operations within state machines
- Proper handling of concurrent accesses

### 3.3 Performance Considerations
- Minimal blocking on page switches
- Efficient FIFO management
- Optimized notification system
- Clean handling of edge cases

### 3.4 Error Handling
- Clear error reporting
- Graceful handling of FIFO overflow
- Safe page switching
- Protection against invalid states

## 4. Key Operations

### 4.1 Page Access Handling

- For HW-side pages:
  - Direct memory access
  - Update local access counter
  - Set written flag on writes

- For VP-side pages:
  - Forward access to VP through DPI-C
  - block access

- For access from VP side:
  - async return read data
  - nonblock write/read
  - queue management for concurrent access

### 4.2 Page Switch Process

- RTL side:
  1. Track read and write access counts separately
  2. Maintain separate notification counters for read and write
  3. Notify bridge about page access in two cases:
     a. When notify_interval cycles have passed since last notification
     b. When switching to a different page
  4. Store previous page indices and entries for both read and write

- Bridge side:
  1. Track VP-side access counts
  2. Receive separate notifications for read and write accesses
  3. Compare access counts and make switch decisions
  4. Handle page switches through UMI DMI interface

### 4.3 Access Counting

Counter Management:
- RTL side:
  - Separate counters for read and write accesses
  - Independent notification intervals and counters:
    ```verilog
    reg [31:0] rtl_notify_counter;      // notification counter
    reg [31:0] rtl_last_notify_counter; // Last notification time
    reg [31:0] notify_interval;          // Configurable notification interval
    ```
  - Notification triggers:
    - Timer-based: When current counter exceeds (last_notify + interval)
    - Event-based: When switching to a different page
  - Stores previous page information:

```verilog
bit [63:0] last_access_addr;
bit [63:0] last_write_page_item;
bit [63:0] last_read_page_item;
```

- Software side:
  - Maintains VP access counts
  - Receives separate read/write access notifications through:
```verilog
h2s_notify_page_access_count()  // For read access counts
```
  - Makes page switch decisions based on access patterns
  - The transaction should be done when notify function is called

## 5. Address Translation

- Need to implement HBM address to system address mapping and vice versa
- Support for address translation during page switches

## 6. Page Migration Arbitration

- when local access counter * ratio < remote access counter, then request page migration

## 7. Tests

- Test the basic functionality of the BFM
- Test the address translation functionality
- Test the access counting functionality
- Test the page migration functionality
- Test the page table update functionality
- Test the page table migration functionality
- Test concurrent access scenarios
- Test request buffer handling
